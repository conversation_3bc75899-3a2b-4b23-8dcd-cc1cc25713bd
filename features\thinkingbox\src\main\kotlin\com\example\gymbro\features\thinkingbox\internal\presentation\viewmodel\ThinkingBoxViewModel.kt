package com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.network.router.TokenRouter
import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.domain.reducer.ThinkingReducer
import com.example.gymbro.features.thinkingbox.internal.presentation.contract.ThinkingBoxContract
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class ThinkingBoxViewModel @Inject constructor(
    private val tokenRouter: TokenRouter,
    private val streamingParser: StreamingThinkingMLParser,
    private val domainMapper: DomainMapper,
    private val rawChunkProcessor: com.example.gymbro.features.thinkingbox.domain.processor.RawChunkProcessor,
    private val tokenizerService: com.example.gymbro.core.ai.tokenizer.TokenizerService
) : ViewModel() {
    private val _state = MutableStateFlow(ThinkingBoxContract.State())
    val state: StateFlow<ThinkingBoxContract.State> = _state.asStateFlow()

    private val _effects = MutableSharedFlow<ThinkingBoxContract.Effect>()
    val effects: SharedFlow<ThinkingBoxContract.Effect> = _effects.asSharedFlow()

    private var parseJob: Job? = null
    private var currentMessageId: String? = null

    // 🔥 【Token收集器】统一RAW TOKEN收集和记录（复刻原始架构）
    private val tokenCollector = StringBuilder()
    private var tokenCount = 0
    private val TOKEN_LOG_INTERVAL = 100 // 每100个token记录一次

    // 🔥 【Token收集器】统一RAW TOKEN收集和记录（复刻原始架构）
    private val tokenCollector = StringBuilder()
    private var tokenCount = 0
    private val TOKEN_LOG_INTERVAL = 100 // 每100个token记录一次

    fun sendIntent(intent: ThinkingBoxContract.Intent) {
        viewModelScope.launch {
            handleIntent(intent)
        }
    }

    /**
     * 🔥 【统一Token收集】收集RAW TOKEN并每100个token记录一次（复刻原始架构）
     */
    private fun collectAndLogToken(rawToken: String) {
        // 收集token
        tokenCollector.append(rawToken)
        tokenCount++

        // 每100个token记录一次完整内容
        if (tokenCount % TOKEN_LOG_INTERVAL == 0) {
            val collectedContent = tokenCollector.toString()
            Timber.tag("RAW-TOKEN-COLLECTOR").i(
                "🔥 [Token收集] messageId=$currentMessageId, 已收集${tokenCount}个token, " +
                "总长度=${collectedContent.length}字符\n" +
                "完整内容:\n$collectedContent"
            )
        }

        // 检查关键标签
        if (rawToken.contains("<think>") || rawToken.contains("</think>")) {
            Timber.tag("RAW-TOKEN-COLLECTOR").e(
                "🚨 [关键标签检测] messageId=$currentMessageId, token#$tokenCount 包含think标签: '$rawToken'"
            )
        }
    }

    private suspend fun handleIntent(intent: ThinkingBoxContract.Intent) {
        when (intent) {
            is ThinkingBoxContract.Intent.Initialize -> handleInitialize(intent.messageId)
            is ThinkingBoxContract.Intent.HandleThinkingEvent -> processThinkingEvent(intent.event)
            is ThinkingBoxContract.Intent.PhaseAnimationFinished -> processThinkingEvent(ThinkingEvent.PhaseAnimFinished(intent.phaseId))
            else -> { /* Other intents for UI interaction */ }
        }
    }

    private fun handleInitialize(messageId: String) {
        if (currentMessageId == messageId && parseJob?.isActive == true) {
            Timber.tag("ThinkingBox-VM").d("🔄 Already initialized for messageId: $messageId, skipping")
            return
        }

        Timber.tag("ThinkingBox-VM").i("🚀 [点火信号] Initializing ThinkingBoxViewModel for messageId: $messageId")
        Timber.tag("ThinkingBox-VM").i("🚀 [点火信号] Previous messageId: $currentMessageId, parseJob active: ${parseJob?.isActive}")

        currentMessageId = messageId
        parseJob?.cancel()

        val internalState = ThinkingReducer.createInitialState(messageId)
        var mappingContext = DomainMapper.MappingContext()
        _state.value = convertToContractState(internalState)

        parseJob = viewModelScope.launch {
            try {
                val conversationScope = tokenRouter.getOrCreateScope(messageId)
                Timber.tag("ThinkingBox-VM").i("✅ [数据管道] Got ConversationScope for $messageId. Starting token parsing...")
                Timber.tag("ThinkingBox-VM").i("✅ [数据管道] ConversationScope tokens flow ready, about to subscribe...")

                // 🔥 【Token预处理】使用RawChunkProcessor清洗XML标签（复刻原始架构）
                Timber.tag("ThinkingBox-VM").i("🔄 [Token预处理] Starting token preprocessing for messageId: $messageId")
                val preprocessedTokens = conversationScope.tokens.map { rawToken ->
                    // 🔥 【统一Token收集】收集所有RAW TOKEN并定期记录
                    collectAndLogToken(rawToken)

                    // 🔥 【核心预处理】使用RawChunkProcessor清洗XML标签
                    rawChunkProcessor.preprocess(rawToken)
                }

                streamingParser.parseTokenStream(
                    messageId = messageId,
                    tokens = preprocessedTokens,
                    onEvent = { semanticEvent ->
                        Timber.tag("ThinkingBox-VM").d("📥 [数据管道] Received SemanticEvent: ${semanticEvent::class.simpleName}")
                        val mappingResult = domainMapper.mapSemanticToThinking(semanticEvent, mappingContext)
                        mappingContext = mappingResult.context
                        mappingResult.events.forEach { thinkingEvent ->
                            Timber.tag("ThinkingBox-VM").d("🎯 [数据管道] Processing ThinkingEvent: ${thinkingEvent::class.simpleName}")
                            processThinkingEvent(thinkingEvent)
                        }
                    }
                )
            } catch (e: Exception) {
                Timber.tag("ThinkingBox-VM").e(e, "❌ [数据管道] Failed to initialize token parsing for messageId: $messageId")
            }
        }
    }

    private fun processThinkingEvent(event: ThinkingEvent) {
        val currentInternalState = convertToReducerState(_state.value)
        val newInternalState = ThinkingReducer.reduce(currentInternalState, event)
        if (newInternalState != currentInternalState) {
            _state.value = convertToContractState(newInternalState)

            // Check for completion to fire the effect
            if (newInternalState.isThinkingComplete && !newInternalState.finalMarkdown.isNullOrBlank()) {
                 viewModelScope.launch {
                    _effects.emit(ThinkingBoxContract.Effect.NotifyMessageComplete(
                        currentMessageId ?: "",
                        newInternalState.finalMarkdown
                    ))
                }
            }
        }
    }

    // Helper functions to convert between reducer state and contract state
    private fun convertToContractState(reducerState: ThinkingReducer.ThinkingUiState): ThinkingBoxContract.State {
        return ThinkingBoxContract.State(
            messageId = reducerState.sessionId ?: "",
            phases = reducerState.phases.values.map { com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi(it.id, it.title, it.content, it.isComplete) },
            activePhaseId = reducerState.activePhaseId,
            preThinking = reducerState.preThinking,
            isStreaming = reducerState.isStreaming,
            isThinkingComplete = reducerState.isThinkingComplete,
            finalMarkdown = reducerState.finalMarkdown,
            finalTokens = reducerState.finalTokens,
            finalRichTextReady = reducerState.finalRichTextReady,
            thinkingDuration = reducerState.thinkingDuration,
            totalTokens = reducerState.totalTokens
        )
    }

    private fun convertToReducerState(contractState: ThinkingBoxContract.State): ThinkingReducer.ThinkingUiState {
        return ThinkingReducer.ThinkingUiState(
            sessionId = contractState.messageId,
            phases = LinkedHashMap(contractState.phases.associateBy { it.id }.mapValues { ThinkingReducer.PhaseUi(it.value.id, it.value.title, it.value.content, it.value.isComplete) }),
            activePhaseId = contractState.activePhaseId,
            preThinking = contractState.preThinking,
            isStreaming = contractState.isStreaming,
            isThinkingComplete = contractState.isThinkingComplete,
            finalMarkdown = contractState.finalMarkdown,
            finalTokens = contractState.finalTokens,
            finalRichTextReady = contractState.finalRichTextReady,
            thinkingDuration = contractState.thinkingDuration,
            totalTokens = contractState.totalTokens
        )
    }
}
