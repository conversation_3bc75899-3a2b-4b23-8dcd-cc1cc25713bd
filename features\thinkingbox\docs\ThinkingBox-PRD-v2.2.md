# ThinkingBox PRD v2.2 - 双时序架构产品需求文档

> **文档版本**: v2.2  
> **更新日期**: 2025-07-25  
> **状态**: ✅ 已实现并验证  
> **架构**: 双时序架构 + 724方案修复 + 双时序握手验证

---

## 🎯 产品概述

ThinkingBox是GymBro AI助手的思考过程可视化组件，通过双时序架构实现流畅的思考到富文本渲染体验。

### 核心价值
- **透明思考**：用户可以实时看到AI的思考过程
- **流畅体验**：双时序架构确保无卡顿的视觉过渡
- **富文本支持**：支持Markdown、Mermaid图表等复杂内容

---

## 🏗️ 双时序架构核心

### 架构原则
1. **后台数据时序**：所有XML标签处理只负责数据准备，不触发UI切换
2. **前台UI时序**：onAnimationFinished作为唯一的阶段转换控制器
3. **双握手机制**：数据完成 + UI动画完成 = 真正切换

### 关键组件
- **StreamingThinkingMLParser**：后台XML解析和事件生成
- **ThinkingReducer**：状态管理和双握手检查
- **ThinkingStageCard**：前台UI渲染和动画控制
- **StreamingFinalRenderer**：最终富文本打字机渲染

---

## 📋 完整用户流程

### 流程概览
```
用户发送消息 → Header → perthink → 正式phase → 思考完成 → 富文本显示
```

### 详细流程 + 双时序握手

#### 1. 【握手点1】Header → perthink
- **触发**：检测到`<think>`标签
- **后台数据时序**：`<think>` → `TagOpened("think")` → `PhaseStart("perthink")`
- **前台UI时序**：Header渐隐 → perthink显示
- **状态切换**：`activePhaseId = "perthink"`
- **用户体验**：看到"Bro is thinking"标题，内容实时更新

#### 2. 【握手点2】perthink → 正式phase
- **触发**：检测到`</think>`标签
- **后台数据时序**：`</think>` → `PreThinkEnd` → `phase.isComplete = true`
- **前台UI时序**：perthink动画完成 → `onAnimationFinished("perthink")` → `PhaseAnimFinished("perthink")`
- **双握手检查**：`phase.isComplete && event.id == activePhaseId`
- **状态切换**：`activePhaseId = nextPhaseId`
- **用户体验**：平滑过渡到正式思考阶段

#### 3. 【握手点3】正式phase → 思考完成
- **触发**：检测到`</thinking>`标签
- **后台数据时序**：`</thinking>` → `ThinkingEnd` → 标记数据结束
- **前台UI时序**：最后phase动画完成 → `PhaseAnimFinished(lastPhaseId)`
- **完成条件**：`nextPhaseId == null && phases.all { it.isComplete }`
- **状态切换**：`isThinkingComplete = true, activePhaseId = null`
- **用户体验**：思考框折叠，显示思考完成状态

#### 4. 【握手点4】思考完成 → 富文本显示
- **触发**：检测到`<final>`标签
- **后台数据时序**：`<final>` → `FinalStart` → `finalContentArrived = true`
- **前台UI时序**：思考完成 + final内容到达 → `FinalRenderingReady` → `finalRichTextReady = true`
- **触发条件**：`isThinkingCompleted && finalContentArrived && !finalRichTextReady && !summaryTextVisible`
- **用户体验**：看到打字机效果的富文本内容逐步显示

---

## 🎨 UI设计规范

### ThinkingStageCard
- **标题样式**：金属字体效果，粗体，字号比正文大1号
- **内容样式**：普通渲染，不使用金属动画
- **渲染器**：使用ProgressiveTextRenderer，参数为renderSpeed和fullText
- **移除元素**：状态提示小点组件

### StreamingFinalRenderer
- **功能**：统一的流式打字机效果
- **移除元素**："正在接收内容"的预设提示
- **性能**：delay(100ms)实现打字机视觉效果

### FinalActionsRow
- **布局**：复制按钮放置在左边
- **Token计数**：格式为`~tokens: 00`，斜体，灰色偏浅

### ScrollToBottomBtn
- **布局**：左右居中对齐（BottomCenter）

---

## 🔧 技术实现要点

### 双时序接口修复
- **参数名称**：使用正确的`renderSpeed`和`fullText`参数
- **枚举值**：`RenderSpeed.FAST`（perthink）和`RenderSpeed.COMFORTABLE`（正式phase）
- **回调函数**：`onAnimationFinished`作为唯一的阶段转换触发器

### 状态管理
- **perthink状态**：确保`preThinking`字段正确映射到UI状态
- **phase队列**：使用LinkedHashMap确保阶段顺序
- **双握手检查**：严格验证数据完成和UI动画完成

### 性能优化
- **内存控制**：单个ThinkingBox实例内存占用 < 50MB
- **渲染性能**：保持60fps流畅体验，最低30fps保证
- **打字机效果**：perthink 50ms间隔，正式phase 100ms间隔

---

## ✅ 验证结果

### 已完成验证项目
- ✅ perthink激活问题修复
- ✅ 双时序接口参数名称回归
- ✅ 四个关键握手点验证通过
- ✅ 完整流程端到端测试
- ✅ UI组件样式规范实现

### 核心成果
双时序架构的核心原则得到完全实现：
- **所有标签处理都在后台**，只负责数据准备
- **所有阶段转换都由UI动画接口控制**，确保用户体验
- **双握手机制**确保数据完成 + UI动画完成 = 真正切换

---

## 📚 相关文档

- `finalmermaid大纲.md` - 技术架构详细规范
- `724方案.md` - UI组件修复方案
- `双时序绑定修复报告.md` - 双时序架构修复记录

---

**本PRD为ThinkingBox模块的产品需求权威标准，已完成v2.2双时序握手验证。**
