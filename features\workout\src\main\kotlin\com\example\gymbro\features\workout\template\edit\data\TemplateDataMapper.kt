package com.example.gymbro.features.workout.template.edit.data

import com.example.gymbro.domain.workout.model.template.TemplateExercise
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.features.workout.template.edit.config.TemplateEditConfig
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import com.example.gymbro.shared.models.workout.TemplateExerciseDto
import com.example.gymbro.shared.models.workout.TemplateSetDto
import com.example.gymbro.shared.models.workout.WorkoutTemplateDto
import kotlinx.serialization.json.Json
import timber.log.Timber
import com.example.gymbro.core.logging.TemplateDebugLogger
import java.util.*
// Phase 5 Migration: Essential extension functions integrated from legacy files
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.core.ui.text.UiText
import kotlinx.serialization.json.*
import com.example.gymbro.domain.workout.model.template.TemplateExercise as DomainExerciseInTemplate

/**
 * 模板数据映射器 - Phase 1 架构重构
 *
 * 负责在不同数据层之间进行转换：
 * UI(DTO) ↔ Domain(Model) ↔ Data(Entity)
 *
 * 设计原则：
 * - 严格遵循 shared-models DTO 规范
 * - 确保数据完整性和类型安全
 * - 支持 JSON 转换规范要求
 * - 提供容错处理机制
 *
 * <AUTHOR> 4.0 sonnet
 */
object TemplateDataMapper {

    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true      // 🔥 确保默认值被编码
        explicitNulls = true       // 🔥 修复：确保所有字段都被包含，包括默认值
        isLenient = true
        coerceInputValues = true   // 🔥 修复：强制输入值转换，提高解析健壮性
    }

    // ==================== UI State → Domain Model ====================

    // P5: 已删除废弃的 mapStateToDomain 方法 - 无调用点，安全删除

    /**
     * 将 TemplateExerciseDto 列表转换为 TemplateExercise 列表
     */
    private fun mapExerciseDtosToTemplateExercises(
        exerciseDtos: List<TemplateExerciseDto>
    ): List<TemplateExercise> {
        // 🔥 调试日志：验证输入数据
        Timber.d("🔧 [TemplateDataMapper] mapExerciseDtosToTemplateExercises 输入: ${exerciseDtos.size} 个动作")

        return exerciseDtos.mapIndexed { index, dto ->
            // 🔥 调试日志：验证每个动作的数据
            Timber.d("🔧 [TemplateDataMapper] 处理动作${index + 1}: ${dto.exerciseName}, customSets=${dto.customSets.size}")
            dto.customSets.forEachIndexed { setIndex, set ->
                Timber.d("🔧 [TemplateDataMapper] 动作组${setIndex + 1}: weight=${set.targetWeight}, reps=${set.targetReps}, rest=${set.restTimeSeconds}s")
            }

            // 序列化 customSets 到 notes 字段以保持数据完整性
            val customSetsJson = if (dto.customSets.isNotEmpty()) {
                try {
                    // 🔥 修复：序列化前验证数据完整性
                    val validatedCustomSets = dto.customSets.mapIndexed { setIndex, set ->
                        // TemplateSetDto 使用 setNumber 而不是 id
                        val validatedSet = if (set.setNumber <= 0) {
                            Timber.w("🔧 [DEBUG-SAVE] 动作${dto.exerciseName} 组${setIndex + 1} setNumber 无效，自动修正")
                            set.copy(setNumber = setIndex + 1)
                        } else {
                            set
                        }

                        // 🔥 删除重置逻辑：保持用户数据完整性，不进行任何数值重置
                        validatedSet.copy(
                            setNumber = setIndex + 1 // 只修正组号，保持其他数据不变
                        )
                    }

                    val jsonString = json.encodeToString(validatedCustomSets)
                    Timber.d("🔧 [DEBUG-SAVE] 序列化 customSets 成功: ${dto.exerciseName}")
                    Timber.d("🔧 [DEBUG-SAVE] 验证后组数=${validatedCustomSets.size}, JSON长度=${jsonString.length}")
                    Timber.d("🔧 [DEBUG-SAVE] JSON内容预览: ${jsonString.take(200)}")

                    // 🔥 修复：验证序列化结果
                    if (jsonString.length > 10000) { // 假设 notes 字段有长度限制
                        Timber.w("🚨 [DEBUG-SAVE] JSON 长度过长 (${jsonString.length})，可能导致数据库截断")
                    }

                    // 🔥 修复：测试反序列化以确保数据完整性
                    try {
                        val testParsed = json.decodeFromString<List<TemplateSetDto>>(jsonString)
                        if (testParsed.size != validatedCustomSets.size) {
                            Timber.e("🚨 [DEBUG-SAVE] 序列化测试失败：组数不匹配 (${testParsed.size} vs ${validatedCustomSets.size})")
                        } else {
                            Timber.d("🔧 [DEBUG-SAVE] 序列化测试通过")
                        }
                    } catch (testException: Exception) {
                        Timber.e(testException, "🚨 [DEBUG-SAVE] 序列化测试失败")
                    }

                    jsonString
                } catch (e: Exception) {
                    Timber.e(e, "🔧 [DEBUG-SAVE] 序列化 customSets 失败: ${dto.exerciseName}")
                    Timber.e("🔧 [DEBUG-SAVE] 失败的 customSets 数据: ${dto.customSets}")
                    null
                }
            } else {
                Timber.w("🔧 [DEBUG-SAVE] 动作 ${dto.exerciseName} 没有 customSets，跳过序列化")
                null
            }

            val notesWithCustomSets = buildString {
                if (dto.notes?.isNotBlank() == true) {
                    append(dto.notes)
                }
                if (customSetsJson != null) {
                    if (isNotEmpty()) append("\n")
                    append("${TemplateEditConfig.CUSTOM_SETS_JSON_MARKER}$customSetsJson")
                }
            }.takeIf { it.isNotBlank() }

            // 🔥 修复每组独立数据问题：Domain模型字段作为汇总信息
            // customSets 是权威数据源，已序列化到 notes 字段
            val effectiveSets = if (dto.customSets.isNotEmpty()) {
                dto.customSets.size
            } else {
                dto.sets
            }

            // 🔥 删除第一组数据覆盖逻辑：保持基础字段独立，不被第一组数据重置
            // customSets 数据已经序列化到 notes 字段，基础字段保持原值

            TemplateExercise(
                id = dto.id,
                exerciseId = dto.exerciseId,
                name = dto.exerciseName,
                order = index,
                sets = effectiveSets,
                // 🔥 修复：使用原始基础字段，不被第一组数据覆盖
                reps = dto.reps,
                restSeconds = dto.restTimeSeconds,
                weight = dto.targetWeight,
                notes = notesWithCustomSets
            )
        }
    }

    // ==================== Domain Model → UI State ====================

    // P5: 已删除废弃的 mapDomainToState 方法 - 无调用点，安全删除

    /**
     * 将 TemplateExercise 列表转换为 TemplateExerciseDto 列表
     *
     * ⚠️ DEPRECATED: Phase 1 已废弃，请使用 mapDtoToState
     * 根据 720修复template.md Phase 1 要求，此方法存在数据污染问题
     */
    @Deprecated("Use mapDtoToState instead - causes data pollution", ReplaceWith("mapDtoToState(dto, currentState)"))
    private fun mapTemplateExercisesToDtos(
        exercises: List<TemplateExercise>
    ): List<TemplateExerciseDto> {
        // 🚨 Phase 1: 此方法已被废弃，抛出异常引导使用新的统一映射方法
        throw UnsupportedOperationException(
            "mapTemplateExercisesToDtos 已在 Phase 1 中废弃。请使用 mapDtoToState 替代。" +
            "原因：Domain→DTO 映射导致数据污染，customSets 应为唯一权威数据源。"
        )
    }

    /**
     * 同步 TemplateExerciseDto 的基础字段和 customSets
     * 🔥 关键修复：customSets 是绝对权威数据源，禁止任何覆盖行为
     */
    fun syncExerciseData(dto: TemplateExerciseDto): TemplateExerciseDto {
        // 🔥 关键修复：如果 customSets 已存在，直接返回，不做任何修改
        if (dto.customSets.isNotEmpty()) {
            Timber.d("🔧 [DATA-PRESERVATION] 动作 ${dto.exerciseName} 已有 customSets (${dto.customSets.size}组)，保持不变")
            dto.customSets.forEachIndexed { index, set ->
                Timber.d("🔧 [DATA-PRESERVATION] 保持组${index + 1}: weight=${set.targetWeight}, reps=${set.targetReps}, rest=${set.restTimeSeconds}s")
            }

            // 只同步组数，其他数据保持不变
            return dto.copy(sets = dto.customSets.size)
        }

        // 🔥 数据验证：检查customSets完整性
        if (dto.customSets.isEmpty()) {
            val errorMsg = "🔥 [DATA-VALIDATION] 动作 ${dto.exerciseName} 缺少 customSets 数据"
            Timber.tag("CRITICAL-DB").e(errorMsg)
            
            // 严格验证：不允许缺少customSets的数据进入系统
            throw IllegalStateException("数据完整性验证失败：动作 ${dto.exerciseName} 缺少必需的组数据")
        }

        // 验证customSets数据完整性
        if (!validateIndependentSetData(dto)) {
            val errorMsg = "🔥 [DATA-VALIDATION] 动作 ${dto.exerciseName} customSets 数据不完整"
            Timber.tag("CRITICAL-DB").e(errorMsg)
            
            throw IllegalStateException("数据完整性验证失败：动作 ${dto.exerciseName} 组数据格式错误")
        }

        return dto // 数据验证通过，直接返回
    }

    /**
     * 验证每组数据的完整性
     * 确保每组都有独立的JSON数据结构
     */
    fun validateIndependentSetData(dto: TemplateExerciseDto): Boolean {
        if (dto.customSets.isEmpty()) return false

        // 检查每组是否有独立的数据
        val setNumbers = dto.customSets.map { it.setNumber }.toSet()
        val expectedSetNumbers = (1..dto.customSets.size).toSet()

        return setNumbers == expectedSetNumbers &&
               dto.customSets.all { set ->
                   set.targetReps > 0 &&
                   set.restTimeSeconds >= 0 &&
                   set.targetWeight >= 0f
               }
    }

    /**
     * 从 notes 字段中提取 customSets 数据
     * 🔥 修复：删除所有导致数据重置的逻辑，保持数据完整性
     */
    private fun extractCustomSetsFromNotes(notes: String?): Pair<String?, List<TemplateSetDto>> {
        Timber.d("🔧 [DEBUG-LOAD] extractCustomSetsFromNotes 开始解析:")
        Timber.d("🔧 [DEBUG-LOAD] notes 长度: ${notes?.length}")
        Timber.d("🔧 [DEBUG-LOAD] notes 内容预览: ${notes?.take(100)}")

        if (notes.isNullOrBlank()) {
            Timber.d("🔧 [DEBUG-LOAD] notes 为空，保持现有状态")
            return null to emptyList() // 这里保留，因为确实没有数据
        }

        val customSetsMarker = TemplateEditConfig.CUSTOM_SETS_JSON_MARKER
        val markerIndex = notes.indexOf(customSetsMarker)
        Timber.d("🔧 [DEBUG-LOAD] customSetsMarker='$customSetsMarker', markerIndex=$markerIndex")

        if (markerIndex == -1) {
            Timber.d("🔧 [DEBUG-LOAD] 未找到 customSets 标记，保持原始 notes")
            return notes to emptyList() // 这里保留，因为确实没有 customSets 数据
        }

        // 🔥 根本修复：保留原始完整的 notes 数据，避免数据丢失
        val beforeMarker = notes.substring(0, markerIndex).trim()
        val actualNotes = if (beforeMarker.isNotBlank()) {
            notes // 保留完整的原始数据，包含用户备注和customSets
        } else {
            notes // 🔥 关键修复：即使前面没有用户备注，也保留完整的notes（包含customSets）
        }
        val customSetsJson = notes.substring(markerIndex + customSetsMarker.length)
        Timber.d("🔧 [DEBUG-LOAD] 提取的 customSetsJson 长度: ${customSetsJson.length}")
        Timber.d("🔧 [DEBUG-LOAD] customSetsJson 内容预览: ${customSetsJson.take(200)}")

        val customSets = try {
            val parsed = json.decodeFromString<List<TemplateSetDto>>(customSetsJson)
            Timber.d("🔧 [DEBUG-LOAD] 反序列化成功，customSets 数量: ${parsed.size}")
            parsed.forEachIndexed { index, set ->
                Timber.d("🔧 [DEBUG-LOAD] 解析组${index + 1}: weight=${set.targetWeight}, reps=${set.targetReps}, rest=${set.restTimeSeconds}s, setNumber=${set.setNumber}")
            }

            // 🔥 修复：验证解析后的数据完整性
            if (parsed.isEmpty()) {
                Timber.w("🔧 [DEBUG-LOAD] 解析结果为空列表，可能存在数据问题")
            }

            // 🔥 修复：验证每个组的数据完整性
            val validatedSets = parsed.mapIndexed { index, set ->
                if (set.setNumber <= 0) {
                    Timber.w("🔧 [DEBUG-LOAD] 组${index + 1} setNumber 无效，自动修正")
                    set.copy(setNumber = index + 1)
                } else {
                    set
                }
            }

            validatedSets
        } catch (e: Exception) {
            Timber.e(e, "🔧 [DEBUG-LOAD] 反序列化 customSets 失败")
            Timber.e("🔧 [DEBUG-LOAD] 失败的 JSON 长度: ${customSetsJson.length}")
            Timber.e("🔧 [DEBUG-LOAD] 失败的 JSON 内容: ${customSetsJson}")

            // 🔥 修复：尝试备用解析策略
            try {
                Timber.d("🔧 [DEBUG-LOAD] 尝试备用解析策略...")

                // 尝试清理 JSON 字符串（移除可能的控制字符）
                val cleanedJson = customSetsJson.trim()
                    .replace("\r\n", "\n")
                    .replace("\r", "\n")
                    .replace("\\n", "\n")

                val backupParsed = json.decodeFromString<List<TemplateSetDto>>(cleanedJson)
                Timber.d("🔧 [DEBUG-LOAD] 备用解析成功，数量: ${backupParsed.size}")
                backupParsed
            } catch (backupException: Exception) {
                Timber.e(backupException, "🔧 [DEBUG-LOAD] 备用解析也失败，保持原始数据")

                // 🔥 关键修复：不返回空列表，而是保持原始数据不变
                Timber.e("🚨 [DATA-PRESERVATION] customSets 数据解析失败，保持原始 notes 数据")
                Timber.e("🚨 [DATA-PRESERVATION] 原始 notes 长度: ${notes?.length}")
                Timber.e("🚨 [DATA-PRESERVATION] customSetsMarker 位置: $markerIndex")

                // 🔥 删除重置逻辑：不返回 emptyList()，而是保持现有状态
                // 这样可以避免数据丢失，让上层逻辑处理
                throw Exception("JSON 解析失败，保持原始数据")
            }
        }

        return actualNotes to customSets
    }

    // ==================== Domain Model → shared-models DTO ====================

    /**
     * 将 Domain 模型转换为 shared-models DTO
     * 用于缓存和 Function Call 兼容性
     */
    fun mapDomainToDto(template: WorkoutTemplate): WorkoutTemplateDto {
        return WorkoutTemplateDto(
            id = template.id,
            name = template.name,
            description = template.description ?: "",
            exercises = template.exercises.map { exercise ->
                // 🔥 修复：安全提取 customSets，避免数据重置
                val (actualNotes, customSets) = try {
                    extractCustomSetsFromNotes(exercise.notes)
                } catch (e: Exception) {
                    Timber.w(e, "🔧 [DATA-PRESERVATION] Domain转DTO时提取 customSets 失败，从基础字段重建: ${exercise.name}")
                    // 🔥 修复：不返回空列表，而是从基础字段重建 customSets
                    val rebuiltCustomSets = (1..exercise.sets).map { setNumber ->
                        TemplateSetDto(
                            setNumber = setNumber,
                            targetWeight = exercise.weight ?: 0f,
                            targetReps = exercise.reps,
                            restTimeSeconds = exercise.restSeconds,
                            targetDuration = null,
                            rpe = null
                        )
                    }
                    Timber.d("🔧 [DATA-PRESERVATION] Domain转DTO重建了 ${rebuiltCustomSets.size} 组数据")
                    exercise.notes to rebuiltCustomSets
                }

                TemplateExerciseDto(
                    id = exercise.id,
                    exerciseId = exercise.exerciseId,
                    exerciseName = exercise.name,
                    sets = exercise.sets,
                    reps = exercise.reps,
                    targetWeight = exercise.weight,
                    restTimeSeconds = exercise.restSeconds,
                    notes = actualNotes,
                    customSets = customSets
                )
            },
            difficulty = mapDifficultyToDifficultyEnum(template.difficulty),
            category = com.example.gymbro.shared.models.workout.TemplateCategory.STRENGTH,
            source = com.example.gymbro.shared.models.workout.TemplateSource.USER,
            createdAt = template.createdAt,
            updatedAt = template.updatedAt,
            version = template.currentVersion
        )
    }

    // ==================== 辅助函数 ====================

    private fun generateDefaultTemplateName(): String {
        return TemplateEditConfig.DEFAULT_TEMPLATE_NAME
    }

    private fun extractTargetMuscleGroups(exercises: List<TemplateExerciseDto>): List<String> {
        // 基于动作名称推断目标肌群
        return exercises.mapNotNull { exercise ->
            TemplateEditConfig.EXERCISE_MUSCLE_GROUP_MAPPING.entries.find { (keyword, _) ->
                exercise.exerciseName.contains(keyword, ignoreCase = true)
            }?.value
        }.distinct()
    }

    private fun calculateDifficulty(exercises: List<TemplateExerciseDto>): Int {
        return when {
            exercises.size <= 3 -> 1
            exercises.size <= 6 -> 2
            exercises.size <= 9 -> 3
            exercises.size <= 12 -> 4
            else -> 5
        }.coerceIn(1, 5)
    }

    private fun calculateEstimatedDuration(exercises: List<TemplateExerciseDto>): Int {
        val baseTime = exercises.sumOf { exercise ->
            val setTime = exercise.sets * TemplateEditConfig.ESTIMATED_SET_TIME_SECONDS
            val restTime = exercise.sets * (exercise.restTimeSeconds / 60) // 休息时间转分钟
            setTime + restTime
        }
        return (baseTime / 60).coerceAtLeast(TemplateEditConfig.MIN_WORKOUT_DURATION_MINUTES)
    }

    private fun mapDifficultyToDifficultyEnum(difficulty: Int?): com.example.gymbro.shared.models.workout.Difficulty {
        return when (difficulty) {
            1 -> com.example.gymbro.shared.models.workout.Difficulty.EASY
            2 -> com.example.gymbro.shared.models.workout.Difficulty.MEDIUM
            3, 4, 5 -> com.example.gymbro.shared.models.workout.Difficulty.HARD
            else -> com.example.gymbro.shared.models.workout.Difficulty.MEDIUM
        }
    }

    // ==================== Phase 1: 新增单向映射方法 ====================

    /**
     * P0: 将 UI 状态转换为 DTO（替代 mapStateToDomain）
     *
     * P0 修复要点：
     * - 统一模板ID逻辑：禁止再次生成UUID，确保编辑现有模板时保持原始ID
     * - 添加CRITICAL级别日志：追踪每次转换的模板ID变化和customSets明细
     * - customSets 成为唯一权威数据源
     */
    fun mapStateToDto(state: TemplateEditContract.State): WorkoutTemplateDto {
        Timber.tag("CRITICAL-DB").i("🔥 [P0-MAPPER-START] mapStateToDto 开始 - 单向映射")
        Timber.tag("CRITICAL-DB").i("🔥 [P0-MAPPER-START] 模板=${state.templateName}, 动作数=${state.exercises.size}")

        // 🔥 P0: 统一模板ID逻辑 - 确保编辑现有模板时保持原始ID，避免创建重复模板
        val originalId = state.template?.id
        val isExistingTemplate = originalId?.isNotBlank() == true
        val finalId = if (isExistingTemplate) {
            // 如果有原始ID，说明是编辑现有模板，必须保持原ID
            Timber.tag("CRITICAL-DB").i("🔥 [P0-ID-PRESERVE] 编辑现有模板，保持原始ID: $originalId")
            originalId
        } else {
            // 只有在创建新模板时才生成新ID
            val newId = WorkoutTemplateDto.generateId()
            Timber.tag("CRITICAL-DB").i("🔥 [P0-ID-GENERATE] 新建模板，生成新ID: $newId")
            newId
        }

        // 🔥 P0: CRITICAL日志 - 追踪模板ID变化
        Timber.tag("CRITICAL-DB").i("🔥 [P0-ID-FINAL] 模板ID确定: 原始='$originalId' -> 最终='$finalId' (${if (isExistingTemplate) "保持原ID" else "生成新ID"})")

        // 🔥 P0: customSets 是唯一权威数据源，添加详细的数据追踪
        val exerciseDtos = state.exercises.mapIndexed { exerciseIndex, exercise ->
            Timber.tag("CRITICAL-DB").i("🔥 [P0-EXERCISE-${exerciseIndex + 1}] 处理动作: ${exercise.exerciseName}, customSets=${exercise.customSets.size}")

            // 🔥 P0: 验证 customSets 完整性，抛异常代替静默回退
            if (exercise.customSets.isEmpty()) {
                val errorMsg = "🚨 [P0-PROTECTION] 动作 ${exercise.exerciseName} 的 customSets 为空，拒绝保存以防数据丢失"
                Timber.tag("CRITICAL-DB").e(errorMsg)
                throw IllegalStateException(errorMsg)
            }

            // 🔥 P0: CRITICAL日志 - 记录每组数据明细
            exercise.customSets.forEachIndexed { setIndex, set ->
                Timber.tag("CRITICAL-DB").i("🔥 [P0-SET-DATA] 动作${exerciseIndex + 1}-组${setIndex + 1}: weight=${set.targetWeight}, reps=${set.targetReps}, rest=${set.restTimeSeconds}s")
            }

            // 直接使用 TemplateExerciseDto，不经过 Domain 模型
            com.example.gymbro.shared.models.workout.TemplateExerciseDto(
                id = exercise.id,
                exerciseId = exercise.exerciseId,
                exerciseName = exercise.exerciseName,
                sets = exercise.customSets.size, // 🔥 从 customSets 获取组数
                reps = exercise.reps,
                targetWeight = exercise.targetWeight,
                restTimeSeconds = exercise.restTimeSeconds, // 🔥 修复参数名
                notes = exercise.notes,
                customSets = exercise.customSets // 🔥 customSets 是权威数据源
            )
        }

        return WorkoutTemplateDto(
            id = finalId, // 🔥 修复：使用生成的ID
            name = state.templateName,
            description = state.templateDescription,
            exercises = exerciseDtos,
            difficulty = com.example.gymbro.shared.models.workout.Difficulty.MEDIUM, // 🔥 修复类型匹配
            createdAt = state.template?.createdAt ?: System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
            version = 1, // 🔥 修复参数名
            currentVersion = state.currentVersion,
            isDraft = state.isDraft,
            isPublished = state.isPublished,
            lastPublishedAt = state.lastPublishedAt
        )
    }

    /**
     * Phase 1: 将 DTO 转换为 UI 状态（替代 mapDomainToState）
     *
     * 根据 722修复方案1.md Phase 1 要求：
     * - 新增并替代：mapDtoToState(dto)
     * - 修复加载路径：解析失败时报警并保持原数据/中止，不可再根据基础字段重建覆盖
     * - 🔥 关键修复：保持 customSets 数据完整性，防止权重丢失
     */
    fun mapDtoToState(
        dto: WorkoutTemplateDto,
        currentState: TemplateEditContract.State
    ): TemplateEditContract.State {
        Timber.tag("CRITICAL-LOAD").i("🔥 [PHASE1-NEW] mapDtoToState 开始 - 单向映射")
        Timber.tag("CRITICAL-LOAD").i("🔥 [PHASE1-NEW] 模板=${dto.name}, 动作数=${dto.exercises.size}")

        // 🔥 Phase 1: 直接使用 DTO 中的 customSets，不经过 Domain 模型污染
        val exerciseDtos = dto.exercises.map { exercise ->
            Timber.tag("CRITICAL-LOAD").i("🔥 [PHASE1-NEW] 处理动作: ${exercise.exerciseName}, customSets=${exercise.customSets.size}")

            // 🔥 关键修复：验证并确保每组数据的完整性
            val validatedCustomSets = exercise.customSets.mapIndexed { index, set ->
                Timber.tag("CRITICAL-LOAD").i("🔥 [PHASE1-NEW] 组${index + 1}: weight=${set.targetWeight}, reps=${set.targetReps}, rest=${set.restTimeSeconds}s")

                // 🔥 修复：只在权重真正异常时才修正，不要覆盖有效的权重数据
                // 注意：targetWeight 为 0.0 是有效值（空杠训练），只有 null 或负数才是异常
                if (set.targetWeight != null && set.targetWeight >= 0f) {
                    // 权重数据有效，保持原值
                    set
                } else {
                    // 权重数据异常，使用基础权重（但基础权重也可能是0，这是正常的）
                    Timber.tag("CRITICAL-LOAD").w("🚨 [DATA-INTEGRITY] 组${index + 1} 权重异常(${set.targetWeight})，使用基础权重")
                    set.copy(targetWeight = exercise.targetWeight ?: 0f)
                }
            }

            // 🔥 修复：如果 customSets 为空，从基础字段重建，避免数据丢失
            val finalCustomSets = if (validatedCustomSets.isEmpty()) {
                Timber.tag("CRITICAL-LOAD").w("🚨 [EMERGENCY-RECOVERY] 动作 ${exercise.exerciseName} customSets为空，从基础字段重建")
                val effectiveSets = if (exercise.sets > 0) exercise.sets else 3
                (1..effectiveSets).map { setNumber ->
                    TemplateSetDto(
                        setNumber = setNumber,
                        targetWeight = exercise.targetWeight ?: 0f,
                        targetReps = exercise.reps,
                        restTimeSeconds = exercise.restTimeSeconds,
                        targetDuration = null,
                        rpe = null
                    )
                }
            } else {
                validatedCustomSets
            }

            // 直接转换为 TemplateExerciseDto，保持 customSets 完整性
            TemplateExerciseDto(
                id = exercise.id,
                exerciseId = exercise.exerciseId,
                exerciseName = exercise.exerciseName,
                sets = finalCustomSets.size, // 🔥 从 customSets 获取组数
                reps = exercise.reps,
                targetWeight = exercise.targetWeight,
                restTimeSeconds = exercise.restTimeSeconds,
                notes = exercise.notes,
                customSets = finalCustomSets // 🔥 customSets 是权威数据源
            )
        }

        return currentState.copy(
            templateName = dto.name,
            templateDescription = dto.description,
            exercises = exerciseDtos,
            currentUserId = currentState.currentUserId, // 🔥 修复：DTO 中没有 userId 字段
            currentVersion = dto.currentVersion ?: 1, // 🔥 修复：处理可空类型
            isDraft = dto.isDraft ?: true, // 🔥 修复：处理可空类型
            isPublished = dto.isPublished ?: false, // 🔥 修复：处理可空类型
            lastPublishedAt = dto.lastPublishedAt,
            isLoading = false,
            error = null
        )
    }
}

// ==================== Phase 5 Migration: Essential Extension Functions ====================
// Integrated from legacy files to complete the architecture refactoring

/**
 * Exercise转换为TemplateExerciseDto
 * 🔥 Phase 5: 用于从动作库添加动作到模板
 */
fun Exercise.toTemplateExerciseDto(): TemplateExerciseDto {
    // 正确提取UiText的字符串值
    val nameText = this.name
    val exerciseName =
        when (nameText) {
            is UiText.DynamicString -> nameText.value
            is UiText.StringResource -> "动作名称" // 临时处理，实际需要解析资源
            else -> "未知动作"
        }

    // 🔥 关键修复：为新添加的动作生成默认的 customSets
    val defaultCustomSets = (1..3).map { setNumber ->
        TemplateSetDto(
            setNumber = setNumber,
            targetWeight = 0f,
            targetReps = 10,
            restTimeSeconds = 90,
            targetDuration = null,
            rpe = null
        )
    }

    return TemplateExerciseDto(
        id = com.example.gymbro.shared.models.workout.WorkoutTemplateDto.generateId(),
        exerciseId = this.id,
        exerciseName = exerciseName,
        sets = 3, // 默认组数
        reps = 10, // 默认次数
        targetWeight = 0f, // 🔥 修复：使用默认值而不是 null
        restTimeSeconds = 90, // 默认休息时间
        notes = null,
        customSets = defaultCustomSets, // 🔥 关键修复：包含完整的 customSets
    )
}

/**
 * DomainExerciseInTemplate转换为TemplateExerciseDto
 * 🔥 Phase 5: 用于从数据库加载数据到模板编辑器
 */
fun DomainExerciseInTemplate.toTemplateExerciseDto(): TemplateExerciseDto {
    // 🔥 Phase 0: 关键加载日志 - 追踪数据转换起点
    Timber.tag("CRITICAL-LOAD").i("🔥 [PHASE0-LOAD-START] Domain→DTO转换: ${this.name}, notes长度=${this.notes?.length ?: 0}")

    // 使用辅助函数获取动作名称
    val exerciseName = getExerciseNameById(this.exerciseId)

    // 🔥 Phase 0 修复：移除静默失败，使用严格错误处理防止数据丢失
    val (actualNotes, customSets) = try {
        parseNotesAndCustomSets(this.notes)
    } catch (e: Exception) {
        // 🚨 Phase 0: 不再静默返回空列表，而是抛出异常保护数据
        Timber.tag("CRITICAL-LOAD").e(e, "🚨 [PHASE0-PROTECTION] customSets 解析失败，拒绝数据丢失: ${this.name}")
        throw IllegalStateException("customSets 解析失败，无法安全加载模板数据: ${this.name}", e)
    }

    // 🔥 Phase 0: 关键数据追踪 - 记录解析后的 customSets 状态
    Timber.tag("CRITICAL-LOAD").i("🔥 [PHASE0-LOAD-DATA] 解析完成: ${this.name}, customSets数量=${customSets.size}")
    customSets.forEachIndexed { index, set ->
        Timber.tag("CRITICAL-LOAD").i("🔥 [PHASE0-LOAD-DATA] 组${index + 1}: weight=${set.targetWeight}, reps=${set.targetReps}, rest=${set.restTimeSeconds}s")
    }

    return TemplateExerciseDto(
        id = com.example.gymbro.shared.models.workout.WorkoutTemplateDto.generateId(),
        exerciseId = this.exerciseId,
        exerciseName = exerciseName,
        sets = this.sets,
        reps = this.reps, // TemplateExercise直接使用Int
        targetWeight = this.weight, // TemplateExercise直接使用Float?
        restTimeSeconds = this.restSeconds,
        notes = actualNotes,
        customSets = customSets, // 🔥 恢复的 customSets 数据
    )
}

/**
 * TemplateExerciseDto转换为DomainExerciseInTemplate
 * 🔥 Phase 5: 用于保存模板数据到数据库
 */
fun TemplateExerciseDto.toDomainExerciseInTemplate(): DomainExerciseInTemplate {
    // 🔥 Phase 0: 关键保存日志 - 追踪数据转换起点
    Timber.tag("CRITICAL-SAVE").i("🔥 [PHASE0-SAVE-START] DTO→Domain转换: ${this.exerciseName}, customSets=${this.customSets.size}")

    // 🔥 Phase 0: 验证 customSets 完整性
    if (this.customSets.isEmpty()) {
        val errorMsg = "🚨 [PHASE0-PROTECTION] 动作 ${this.exerciseName} 的 customSets 为空，拒绝保存"
        Timber.tag("CRITICAL-SAVE").e(errorMsg)
        throw IllegalStateException(errorMsg)
    }

    this.customSets.forEachIndexed { index, set ->
        Timber.tag("CRITICAL-SAVE").i("🔥 [PHASE0-SAVE-DATA] 组${index + 1}: weight=${set.targetWeight}, reps=${set.targetReps}, rest=${set.restTimeSeconds}s")
    }

    // 🔥 Phase 0: 序列化 customSets 到 notes 字段
    val customSetsJson = try {
        Json.encodeToString(this.customSets)
    } catch (e: Exception) {
        Timber.tag("CRITICAL-SAVE").e(e, "🚨 [PHASE0-PROTECTION] customSets 序列化失败: ${this.exerciseName}")
        throw IllegalStateException("customSets 序列化失败，无法安全保存: ${this.exerciseName}", e)
    }

    val notesWithCustomSets = buildString {
        if (<EMAIL>?.isNotBlank() == true) {
            append(<EMAIL>)
        }
        if (isNotEmpty()) append("\n")
        append("${TemplateEditConfig.CUSTOM_SETS_JSON_MARKER}$customSetsJson")
    }

    Timber.tag("CRITICAL-SAVE").i("🔥 [PHASE0-SAVE-COMPLETE] 序列化完成: ${this.exerciseName}, notes长度=${notesWithCustomSets.length}")

    return DomainExerciseInTemplate(
        id = this.id,
        exerciseId = this.exerciseId,
        name = this.exerciseName,
        order = 0, // 将在上层设置正确的顺序
        sets = this.customSets.size, // 🔥 从 customSets 获取组数
        reps = this.reps,
        restSeconds = this.restTimeSeconds,
        weight = this.targetWeight,
        notes = notesWithCustomSets // 🔥 包含序列化的 customSets
    )
}

// ==================== Helper Functions ====================

/**
 * 解析 notes 字段中的 customSets 数据
 * 🔥 Phase 5: 模板编辑系统的辅助函数
 */
private fun parseNotesAndCustomSets(notes: String?): Pair<String?, List<TemplateSetDto>> {
    if (notes.isNullOrBlank()) {
        return null to emptyList()
    }

    val customSetsMarker = TemplateEditConfig.CUSTOM_SETS_JSON_MARKER
    val markerIndex = notes.indexOf(customSetsMarker)

    if (markerIndex == -1) {
        return notes to emptyList()
    }

    val beforeMarker = notes.substring(0, markerIndex).trim()
    val actualNotes = if (beforeMarker.isNotBlank()) beforeMarker else null
    val customSetsJson = notes.substring(markerIndex + customSetsMarker.length)

    val customSets = try {
        Json.decodeFromString<List<TemplateSetDto>>(customSetsJson)
    } catch (e: Exception) {
        Timber.e(e, "Failed to parse customSets JSON")
        emptyList()
    }

    return actualNotes to customSets
}

/**
 * 根据动作ID获取动作名称
 * 🔥 Phase 5: 模板编辑系统的辅助函数
 */
private fun getExerciseNameById(exerciseId: String): String {
    // 这里应该从动作数据库或缓存中获取动作名称
    // 临时实现，返回默认名称
    return "动作 $exerciseId"
}
