请按照以下具体要求修复 ThinkingBox 模块的时序问题和 UI 组件，确保严格遵循双时序架构：

> **更新状态**: ✅ 已完成 (2025-07-25)
> **验证结果**: 双时序握手机制验证通过，完整流程正常工作

## 核心问题修复

### 1. 时序架构修复
- **参考基准**: 使用 `git 59b153ae` 作为正确时序匹配的技术参考
- **双时序要求**: 严格匹配双时序架构，确保数据时序与渲染时序正确协调
- **ThinkingStageCard 稳定性**: 修复当前不稳定的时序判断逻辑，确保动画回调参数正确

### 2. XML 标签处理流程（严格按照 finalmermaid大纲.md）
```
<think>标签 → perthink 阶段
<thinking> → 正式 phase 开始
<phase id="..."> → 控制 phase 刷新
  <title>标题</title> → 标题更新
  文本内容 → 内容流式显示
</phase> → phase 完成，双握手切换
</thinking> → 赋予 phase id="final"
```

### 3. 最终富文本渲染流程
- **phase id="final"** 激活后：
    - 思考框关闭并渐隐
    - 显示 SimpleSummaryText
    - 开始最终富文本渲染
- **`<final>` 标签处理**：
    - 只在后台开始渲染
    - 缓存所有富文本 markdown 和 mermaid 语法
    - 准备好后进行流式渲染
    - `</final>` 标签不做任何时序传递记录

## 具体文件修改要求

### `ThinkingStageCard.kt`
- **标题样式**: 使用金属字体效果，粗体，字号比正文大 1 号
- **正文内容**: 普通渲染，不使用金属动画效果
- **移除**: 状态提示小点组件

### `StreamingFinalRenderer.kt`
- **移除**: "正在接收内容"的预设提示
- **统一渲染器**: 作为打字机效果的统一渲染器

### `ScrollToBottomBtn.kt`
- **布局**: 左右居中对齐

### `FinalActionsRow.kt`
- **复制按钮**: 放置在左边
- **Token 计数**:
    - 格式：`~tokens: 00`
    - 样式：斜体，灰色偏浅

### `ThinkingHeader.kt`
- **职责**: 用户发送消息后立即展现，减少等待时间
- **生命周期**: 等待 perthink 开始后渐隐消失让位

## 渲染完成后的处理
- 使用 `AutoScrollManager.kt` 管理滚动
- 渲染结束后显示 `FinalActionsRow.kt`
- **重要**: phase id="final" 激活后，后续标签不需要任何时序传递，全部内容进入缓存+后台渲染

请确保所有修改都符合双时序架构要求，并参考 `finalmermaid大纲.md` 中的需求规范。

---

## 验证结果 (2025-07-25)

### ✅ 已完成修复项目

#### 1. 双时序架构核心修复
- **perthink激活问题**：修复<think>标签处理，确保TagOpened事件正确发送
- **双时序接口回归**：修复ProgressiveTextRenderer参数名称（renderSpeed, fullText）
- **双时序分离**：确保后台数据时序 + 前台UI时序正确分离

#### 2. 完整流程验证
- **Header → perthink**：握手机制正常
- **perthink → 正式phase**：双握手检查通过
- **正式phase → 思考完成**：时序协调正确
- **思考完成 → 富文本显示**：FinalRenderingReady触发正常

#### 3. UI组件修复确认
- **ThinkingStageCard**：金属标题 + 普通内容渲染 ✅
- **StreamingFinalRenderer**：统一打字机效果 ✅
- **ScrollToBottomBtn**：居中对齐 ✅
- **FinalActionsRow**：复制按钮左置，token计数格式化 ✅

### 🎯 核心成果
双时序架构的核心原则得到完全实现：
- **所有标签处理都在后台**，只负责数据准备
- **所有阶段转换都由UI动画接口控制**，确保用户体验
- **双握手机制**确保数据完成 + UI动画完成 = 真正切换
