package com.example.gymbro.core.logging

import timber.log.Timber
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * 日志优先级常量
 *
 * 提供与 Android Log 类相同的常量值，但不依赖于 Android API。
 * 这使得代码可以在非 Android 环境中使用相同的逻辑。
 */
object LogPriority {
    const val VERBOSE = 2
    const val DEBUG = 3
    const val INFO = 4
    const val WARN = 5
    const val ERROR = 6
    const val ASSERT = 7
}

/**
 * Timber 树基类
 *
 * 提供基本功能，同时避免依赖特定平台API。
 */
abstract class BaseTree : Timber.Tree() {
    /**
     * 确定是否应记录具有给定标签和优先级的消息
     */
    override fun isLoggable(
        tag: String?,
        priority: Int,
    ): Boolean {
        return true // 默认记录所有日志，子类可覆盖此方法进行筛选
    }

    /**
     * 过滤敏感信息
     */
    protected fun sanitizeMessage(message: String): String = SensitiveDataFilter.filterSensitiveData(message)
}

/**
 * 调试树
 *
 * 用于开发和测试环境，输出详细日志信息。
 */
class DebugTree : BaseTree() {
    private val logBuffer = ConcurrentLinkedQueue<LogEntry>()
    private val MAX_BUFFER_SIZE = 100

    override fun log(
        priority: Int,
        tag: String?,
        message: String,
        t: Throwable?,
    ) {
        val effectiveTag = TimberManager.applyTagFilter(tag)

        // 添加到循环缓冲区
        addToBuffer(priority, effectiveTag, message, t)

        // 记录日志（具体实现由平台提供）
        performLog(priority, effectiveTag, message, t)
    }

    /**
     * 执行实际日志记录
     *
     * 此方法在不同平台上有不同实现
     */
    protected fun performLog(
        priority: Int,
        tag: String,
        message: String,
        t: Throwable?,
    ) {
        // 基本实现，子类可重写以使用平台特定功能
        val priorityChar =
            when (priority) {
                LogPriority.VERBOSE -> 'V'
                LogPriority.DEBUG -> 'D'
                LogPriority.INFO -> 'I'
                LogPriority.WARN -> 'W'
                LogPriority.ERROR -> 'E'
                LogPriority.ASSERT -> 'A'
                else -> '?'
            }

        val throwableMessage =
            t?.let {
                "\n${it.javaClass.name}: ${it.message}\n${it.stackTraceToString()}"
            } ?: ""

        // 使用Timber记录日志，避免直接使用println
        // 注意：这里是Timber Tree的基础实现，在实际Android环境中会被Android Log替代
        System.out.println("[$priorityChar] $tag: $message$throwableMessage")
    }

    /**
     * 添加日志条目到循环缓冲区
     */
    private fun addToBuffer(
        priority: Int,
        tag: String,
        message: String,
        t: Throwable?,
    ) {
        logBuffer.add(LogEntry(System.currentTimeMillis(), priority, tag, message, t))

        // 保持缓冲区大小限制
        while (logBuffer.size > MAX_BUFFER_SIZE) {
            logBuffer.poll()
        }
    }
}

/**
 * 发布树
 *
 * 用于生产环境，只记录重要日志并支持错误报告。
 */

@Suppress("ktlint:standard:no-consecutive-comments")
/**
 * 发布树
 *
 * 用于生产环境，只记录重要日志并支持错误报告。
 *
 * 特点：
 * 1. 只记录警告和错误级别的日志
 * 2. 过滤敏感信息
 * 3. 支持错误上报
 * 4. 维护循环缓冲区用于调试
 */
open class ReleaseTree : BaseTree() {
    private val logBuffer = ConcurrentLinkedQueue<LogEntry>()
    private val MAX_BUFFER_SIZE = 50

    override fun isLoggable(
        tag: String?,
        priority: Int,
    ): Boolean {
        // 只允许警告和错误级别的日志
        return priority >= LogPriority.WARN
    }

    override fun log(
        priority: Int,
        tag: String?,
        message: String,
        t: Throwable?,
    ) {
        if (!isLoggable(tag, priority)) {
            return
        }

        val effectiveTag = TimberManager.applyTagFilter(tag ?: "GymBro")
        val sanitizedMessage = sanitizeMessage(message)

        // 添加到循环缓冲区
        addToBuffer(priority, effectiveTag, sanitizedMessage, t)

        when (priority) {
            LogPriority.ERROR -> {
                // 记录错误日志并上报
                performLog(priority, effectiveTag, sanitizedMessage, t)
                reportError(effectiveTag, sanitizedMessage, t)
            }
            LogPriority.ASSERT -> {
                // 记录致命错误并立即上报
                performLog(priority, effectiveTag, sanitizedMessage, t)
                reportFatalError(effectiveTag, sanitizedMessage, t)
            }
            else -> {
                // 记录警告日志
                performLog(priority, effectiveTag, sanitizedMessage, t)
            }
        }
    }

    /**
     * 执行实际日志记录
     */
    protected open fun performLog(
        priority: Int,
        tag: String,
        message: String,
        t: Throwable?,
    ) {
        val priorityChar =
            when (priority) {
                LogPriority.WARN -> 'W'
                LogPriority.ERROR -> 'E'
                LogPriority.ASSERT -> 'A'
                else -> '?'
            }

        val throwableMessage =
            t?.let {
                "\n${it.javaClass.name}: ${it.message}\n${it.stackTraceToString()}"
            } ?: ""

        // 使用System.out.println而非println，明确表示这是基础实现
        // 在Android环境中会被AndroidReleaseTree的Log.println替代
        System.out.println("[$priorityChar] $tag: $message$throwableMessage")
    }

    /**
     * 添加日志条目到循环缓冲区
     */
    private fun addToBuffer(
        priority: Int,
        tag: String,
        message: String,
        t: Throwable?,
    ) {
        logBuffer.add(LogEntry(System.currentTimeMillis(), priority, tag, message, t))

        // 保持缓冲区大小限制
        while (logBuffer.size > MAX_BUFFER_SIZE) {
            logBuffer.poll()
        }
    }

    /**
     * 上报错误信息
     */
    protected open fun reportError(
        tag: String,
        message: String,
        t: Throwable?,
    ) {
        // 平台无关的基本实现，子类可重写以实现特定平台的错误上报
    }

    /**
     * 上报致命错误
     */
    protected open fun reportFatalError(
        tag: String,
        message: String,
        t: Throwable?,
    ) {
        // 平台无关的基本实现，子类可重写以实现特定平台的致命错误上报
    }
}

/**
 * 🔥 【重构】ThinkingBox专用 Timber Tree
 *
 * 专门处理ThinkingBox模块的日志需求，支持深度调试和噪音过滤
 * 符合Clean Architecture原则，位于core层避免依赖违规
 */
class ThinkingBoxAwareTree(
    private val loggingConfig: LoggingConfig,
) : BaseTree() {

    override fun isLoggable(tag: String?, priority: Int): Boolean {
        // 全局开关检查
        if (!loggingConfig.isGlobalEnabled()) {
            return false
        }

        return when {
            // 深度调试标签：直接允许
            isDeepDebugTag(tag) -> true

            // TOKEN-FLOW相关：根据配置决定
            isTokenFlowTag(tag) -> loggingConfig.shouldLog("thinkingbox", tag, priority)

            // 高频标签：只允许ERROR级别
            isHighFrequencyTag(tag) -> priority >= LogPriority.ERROR

            // 其他标签：正常检查
            else -> loggingConfig.shouldLog("thinkingbox", tag, priority)
        }
    }

    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        if (!isLoggable(tag, priority)) {
            return
        }

        // 应用标签过滤
        val effectiveTag = TimberManager.applyTagFilter(tag ?: "TB")

        // 过滤噪音日志
        if (shouldFilterNoiseLog(effectiveTag, message)) {
            return
        }

        // 使用sanitized message
        val sanitizedMessage = sanitizeMessage(message)

        // 执行实际日志记录
        performLog(priority, effectiveTag, sanitizedMessage, t)
    }

    private fun isDeepDebugTag(tag: String?): Boolean {
        return tag in setOf(
            "TB-CONTENT", "TB-UI", "PHASE-DEBUG",
            "FINAL-TYPEWRITER", "SEAMLESS-RENDERER", "RAW-TOKEN-COLLECTOR",
            // 🔥 【关键解析器标签】
            "TB-PARSER", "ThinkingBox-VM", "RawChunkProcessor",
            "XmlStreamScanner", "DomainMapper", "ThinkingReducer",
            "StreamingThinkingMLParser", "TB-MAPPER", "TB-REDUCER",
            "TokenRouter", // 🔥 【核心路由调试】
            // 🔥 【Token流调试标签】
            "TOKEN-FLOW", "TB-RAW", "TB-FILTER", "TB-SEM", "TB-MAP", "TB-EVT", "TB-STATE",
        )
    }

    private fun isTokenFlowTag(tag: String?): Boolean {
        return tag?.contains("TOKEN-FLOW", ignoreCase = true) == true ||
            tag?.contains("TB-RAW", ignoreCase = true) == true
    }

    private fun isHighFrequencyTag(tag: String?): Boolean {
        return tag in setOf("TB-MAIN", "TB-STREAM", "TB-RENDER")
    }

    private fun shouldFilterNoiseLog(tag: String?, message: String): Boolean {
        // 过滤重复的token处理日志
        if (message.contains("token", ignoreCase = true) &&
            message.length < 50 &&
            tag?.startsWith("TB-") == true
        ) {
            return true
        }
        return false
    }

    private fun performLog(priority: Int, tag: String, message: String, t: Throwable?) {
        when (priority) {
            LogPriority.VERBOSE -> android.util.Log.v(tag, message, t)
            LogPriority.DEBUG -> android.util.Log.d(tag, message, t)
            LogPriority.INFO -> android.util.Log.i(tag, message, t)
            LogPriority.WARN -> android.util.Log.w(tag, message, t)
            LogPriority.ERROR -> android.util.Log.e(tag, message, t)
            LogPriority.ASSERT -> android.util.Log.wtf(tag, message, t)
        }
    }
}

/**
 * 日志条目数据类
 */
data class LogEntry(
    val timestamp: Long,
    val priority: Int,
    val tag: String,
    val message: String,
    val throwable: Throwable?,
)
