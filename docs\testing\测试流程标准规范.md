# GymBro 测试流程标准规范

## 📋 概述

本文档基于GymBro项目实际测试体系建设经验，制定了完整的测试流程标准规范。遵循Clean Architecture + MVVM架构原则，确保测试的高质量、高效率和标准化。

## 🎯 测试覆盖率目标

| 层级            | 目标覆盖率 | 说明                                  |
| --------------- | ---------- | ------------------------------------- |
| **Domain层**    | ≥90%       | UseCase、Repository接口等核心业务逻辑 |
| **ViewModel层** | ≥75%       | 状态管理、事件处理、UI逻辑            |
| **Data层**      | ≥80%       | Repository实现、数据源、网络层        |
| **UI层**        | ≥60%       | Composable组件、屏幕交互              |

## 🔧 核心工具和技术栈

### 必需依赖
```kotlin
// 测试框架
testImplementation("org.junit.jupiter:junit-jupiter:5.9.2")
testImplementation("org.jetbrains.kotlin:kotlin-test:1.9.0")

// 协程测试
testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3")

// Mock框架
testImplementation("io.mockk:mockk:1.13.8")

// Flow测试
testImplementation("app.cash.turbine:turbine:1.0.0")

// 时间处理
testImplementation("org.jetbrains.kotlinx:kotlinx-datetime:0.4.1")
```

### GymBro核心组件
- **ModernResult<T>**: 统一错误处理
- **UiText**: 本地化文本处理
- **Logger**: 日志记录
- **kotlinx.datetime.Instant**: 时间处理

## 📁 测试模板体系

### 1. Domain层模板 (`domain.kt.template`)
**位置**: `gradle/build-logic/src/main/resources/testSkeletons/domain.kt.template`

**特点**:
- 基于MockK的稳定Mock配置
- 包含ModernResult错误处理测试
- 支持协程和异步操作测试
- 无占位符代码，100%编译通过

**核心结构**:
```kotlin
@OptIn(ExperimentalCoroutinesApi::class)
class {{CLASS}}Test {
    // Test Dependencies - 根据被测试类的实际依赖调整
    private val mockLogger = mockk<Logger>()

    // Test Scope
    private val testScope = TestScope()

    // System Under Test
    private lateinit var useCase: {{CLASS}}

    @Before
    fun setup() {
        // 配置Mock行为
        every { mockLogger.d(any<String>()) } just Runs

        // 初始化被测试对象
    }

    @After
    fun tearDown() {
        clearAllMocks()
    }

    // 基础测试方法模板
}
```

### 2. ViewModel层模板 (`viewmodel.kt.template`)
**位置**: `gradle/build-logic/src/main/resources/testSkeletons/viewmodel.kt.template`

**特点**:
- 集成Turbine用于StateFlow测试
- 支持事件处理和状态变化测试
- 包含错误处理和恢复测试
- 适配MVVM架构模式

**核心结构**:
```kotlin
@OptIn(ExperimentalCoroutinesApi::class)
class {{CLASS}}Test {
    // Test Dependencies
    private val mockUseCase = mockk<SomeUseCase>()
    private val mockErrorHandler = mockk<ModernErrorHandler>()

    // Test Scope and Dispatcher
    private val testScope = TestScope()

    // System Under Test
    private lateinit var viewModel: {{CLASS}}

    @Before
    fun setup() {
        // 配置Mock行为 - 注意StateFlow vs Flow
        every { mockUseCase.state } returns MutableStateFlow(testState)
    }

    @Test
    fun `should handle state changes correctly`() = testScope.runTest {
        viewModel.uiState.test {
            // 使用Turbine测试StateFlow发射序列
            assertEquals(expectedState, awaitItem())
        }
    }
}
```

### 3. Feature层模板 (`feature.kt.template`)
**位置**: `gradle/build-logic/src/main/resources/testSkeletons/feature.kt.template`

**特点**:
- 用于UI组件测试
- 包含Compose测试工具
- 支持UI交互测试

## 🚀 标准化工作流程

### Phase 1: 模板准备和验证

#### 1.1 检查genTest模板
```bash
# 验证模板是否存在且无语法错误
ls gradle/build-logic/src/main/resources/testSkeletons/
```

#### 1.2 模板稳定性验证
- ✅ 确保模板生成的测试文件100%编译通过
- ✅ 包含所有必需的导入和基础结构
- ✅ 无占位符代码或未定义引用
- ✅ Mock配置类型正确

**关键经验**: 先制作一个不会出错的genTest模板，然后根据对应的功能做增加删除内容

### Phase 2: 测试生成和定制

#### 2.1 使用genTest生成基础测试
```bash
# Domain层UseCase测试
./gradlew :domain:genTest "-PgenTestTarget=src/main/kotlin/path/to/UseCase.kt"

# ViewModel测试（需要手动创建，因为genTest会选择feature模板）
# 基于viewmodel.kt.template手动创建
```

#### 2.2 分析被测试类并定制测试内容
1. **分析构造函数和依赖**
   ```kotlin
   // 示例：ManageSubscriptionUseCase的依赖分析
   class ManageSubscriptionUseCase(
       private val subscriptionRepository: SubscriptionRepository,
       private val userSessionManager: UserSessionManager,
       private val logger: Logger
   )
   ```

2. **添加具体的Mock配置**
   ```kotlin
   private val mockSubscriptionRepository = mockk<SubscriptionRepository>()
   private val mockUserSessionManager = mockk<UserSessionManager>()
   private val mockLogger = mockk<Logger>()
   ```

3. **实现针对性的测试方法**
   - 覆盖所有公共方法
   - 包含成功和失败场景
   - 验证依赖调用
   - 测试错误处理

#### 2.3 遵循测试命名规范
```kotlin
@Test
fun `methodName should return expected result when given valid input`()

@Test
fun `methodName should handle error correctly when repository fails`()

@Test
fun `methodName should return error when user is not authenticated`()
```

### Phase 3: 编译验证和运行

#### 3.1 编译验证
```bash
# 编译测试代码
./gradlew :module:compileDebugUnitTestKotlin
```

#### 3.2 运行测试
```bash
# 运行单个模块测试
./gradlew :module:testDebugUnitTest -x jacocoTestReport

# 运行特定测试类
./gradlew :module:testDebugUnitTest --tests "ClassNameTest" -x jacocoTestReport
```

#### 3.3 修复编译错误的优先级
1. **P0 - 导入问题**: 缺少import语句
2. **P1 - Mock配置**: 类型不匹配（如Flow vs StateFlow）
3. **P2 - 方法调用**: 参数类型或数量错误
4. **P3 - 测试逻辑**: 断言或验证逻辑问题

**常见错误修复**:
```kotlin
// 错误：Flow vs StateFlow类型不匹配
every { mockInteractor.state } returns flowOf(testState)
// 正确：
every { mockInteractor.state } returns MutableStateFlow(testState)

// 错误：MockK类型推断问题
every { mockLogger.d(any()) } just Runs
// 正确：
every { mockLogger.d(any<String>()) } just Runs
```

### Phase 4: 覆盖率验证和文档更新

#### 4.1 验证覆盖率达标
- Domain层 ≥90%
- ViewModel层 ≥75%
- 其他层按目标要求

#### 4.2 更新workflow.md记录进度
```markdown
#### X.Y ✅ 模块名称测试实现
- **生成**: 使用genTest模板生成基础测试文件
- **定制**: 根据实际功能添加具体测试方法
- **覆盖**: 列出覆盖的主要功能点
- **验证**: 所有测试编译通过并运行成功
- **结果**: 实现了完整的测试覆盖
```

#### 4.3 归档完成的测试文件
- 确保测试文件位于正确的目录结构
- 验证测试文件命名规范
- 更新模块的测试文档

## 📝 测试编写最佳实践

### 1. Domain层测试要点

#### UseCase测试模式
```kotlin
@Test
fun `useCase should return success when repository succeeds`() = testScope.runTest {
    // Given - 准备成功场景的测试数据
    coEvery { mockRepository.getData() } returns ModernResult.Success(testData)

    // When - 执行被测试的操作
    val result = useCase.execute()

    // Then - 验证结果
    assertTrue(result is ModernResult.Success)
    assertEquals(testData, (result as ModernResult.Success).data)

    // 验证依赖调用
    coVerify { mockRepository.getData() }
    verify { mockLogger.d(any<String>()) }
}
```

#### 错误处理测试
```kotlin
@Test
fun `useCase should handle error correctly when repository fails`() = testScope.runTest {
    // Given - 准备错误场景
    val testError = ModernDataError(
        operationName = "getData",
        errorType = GlobalErrorType.Network.ConnectionFailed,
        category = ErrorCategory.NETWORK
    )
    coEvery { mockRepository.getData() } returns ModernResult.Error(testError)

    // When - 执行操作
    val result = useCase.execute()

    // Then - 验证错误处理
    assertTrue(result is ModernResult.Error)
    val error = (result as ModernResult.Error).error
    assertEquals("getData", error.operationName)
    assertEquals(GlobalErrorType.Network.ConnectionFailed, error.errorType)
}
```

### 2. ViewModel层测试要点

#### StateFlow状态测试
```kotlin
@Test
fun `viewModel should handle loading state correctly`() = testScope.runTest {
    // Given - Mock状态变化
    every { mockUseCase() } returns flowOf(
        ModernResult.Loading,
        ModernResult.Success(testData)
    )

    // When & Then - 使用Turbine测试StateFlow
    viewModel.uiState.test {
        // 验证初始状态
        val initialState = awaitItem()
        assertFalse(initialState.isLoading)

        // 验证loading状态
        val loadingState = awaitItem()
        assertTrue(loadingState.isLoading)

        // 验证成功状态
        val successState = awaitItem()
        assertFalse(successState.isLoading)
        assertEquals(testData, successState.data)

        awaitComplete()
    }
}
```

#### 事件处理测试
```kotlin
@Test
fun `viewModel should handle user events correctly`() = testScope.runTest {
    // Given - ViewModel已初始化
    viewModel = TestViewModel(mockUseCase, mockErrorHandler)

    // When - 触发用户事件
    viewModel.onEvent(UserAction.LoadData)

    // Then - 验证事件处理
    verify { mockUseCase.execute() }

    // 验证状态变化
    viewModel.uiState.test {
        val state = awaitItem()
        assertTrue(state.isLoading)
    }
}
```

### 3. 时间处理测试
```kotlin
@Test
fun `should handle time operations with kotlinx datetime`() = testScope.runTest {
    // Given - 时间相关测试
    val beforeTime = Clock.System.now()
    val capturedData = slot<DataWithTimestamp>()
    coEvery { mockRepository.save(capture(capturedData)) } returns ModernResult.Success(Unit)

    // When - 执行包含时间的操作
    useCase.saveWithTimestamp(testData)

    val afterTime = Clock.System.now()

    // Then - 验证时间处理
    val savedData = capturedData.captured
    assertTrue(savedData.timestamp >= beforeTime)
    assertTrue(savedData.timestamp <= afterTime)
}
```

### 4. Flow测试最佳实践
```kotlin
@Test
fun `should emit correct sequence of states`() = testScope.runTest {
    // Given - Mock Flow数据源
    every { mockRepository.observeData() } returns flowOf(
        ModernResult.Loading,
        ModernResult.Success(testData1),
        ModernResult.Success(testData2)
    )

    // When & Then - 测试Flow发射序列
    useCase.observeData().test {
        assertEquals(ModernResult.Loading, awaitItem())
        assertEquals(ModernResult.Success(testData1), awaitItem())
        assertEquals(ModernResult.Success(testData2), awaitItem())
        awaitComplete()
    }
}
```

## 🔍 质量检查清单

### 编译检查
- [ ] **导入完整性**: 所有必需的import语句都已添加
- [ ] **Mock配置正确**: Mock类型匹配，无类型推断错误
- [ ] **语法正确性**: 测试方法语法正确，无编译错误
- [ ] **依赖引用**: 无未定义的类或方法引用

### 功能检查
- [ ] **成功场景覆盖**: 所有正常流程都有对应测试
- [ ] **错误场景覆盖**: 各种异常情况都有测试
- [ ] **边界条件**: 空值、空列表、极值等边界情况
- [ ] **依赖验证**: 验证Mock对象的方法调用
- [ ] **状态验证**: 验证对象状态的正确变化

### 规范检查
- [ ] **ModernResult使用**: 所有异步操作返回ModernResult
- [ ] **UiText处理**: 用户可见文本使用UiText
- [ ] **时间处理**: 使用kotlinx.datetime.Instant
- [ ] **命名规范**: 测试方法名遵循约定格式
- [ ] **注释完整**: 复杂测试逻辑有适当注释

### 架构检查
- [ ] **层级隔离**: 测试不跨层级依赖
- [ ] **单一职责**: 每个测试方法只测试一个功能点
- [ ] **可读性**: 测试代码清晰易懂
- [ ] **可维护性**: 测试数据和配置易于修改

## 📊 进度跟踪和状态管理

### 工作记录格式
在`docs/tasks/workflow.md`中使用以下格式记录测试进度：

```markdown
#### X.Y ✅/🔄/❌ 模块名称测试实现
- **生成**: 使用genTest模板生成基础测试文件
- **定制**: 根据实际功能添加具体测试方法
- **覆盖**: 列出覆盖的主要功能点
- **验证**: 编译和运行结果
- **结果**: 最终达成情况
```

### 状态指示器
- ✅ **已完成**: 测试编译通过，运行成功，覆盖率达标
- 🔄 **进行中**: 正在开发或调试中
- ❌ **失败/阻塞**: 遇到问题需要解决

### 覆盖率跟踪
```markdown
#### 测试覆盖率达成情况
- **Domain层**: 目标≥90%，已完成UseCase1、UseCase2 ✅
- **ViewModel层**: 目标≥75%，已完成ViewModel1、ViewModel2 ✅
- **Data层**: 目标≥80%，进行中 🔄
```

## 🎯 成功案例参考

### 已验证的测试实现

#### 1. ManageSubscriptionUseCase (Domain层典型案例)
**文件**: `domain/src/test/kotlin/.../ManageSubscriptionUseCaseTest.kt`

**特点**:
- 完整的UseCase测试覆盖
- 包含成功和失败场景
- 验证依赖调用和日志记录
- 使用ModernResult统一错误处理

**关键测试方法**:
```kotlin
fun `createSubscription should return success when repository succeeds`()
fun `createSubscription should return error when repository fails`()
fun `cancelCurrentSubscription should return error when user is not authenticated`()
```

#### 2. QuerySubscriptionUseCase (包含Flow测试的UseCase案例)
**文件**: `domain/src/test/kotlin/.../QuerySubscriptionUseCaseTest.kt`

**特点**:
- 使用Turbine测试Flow发射序列
- 复杂的异步操作测试
- 多种错误场景覆盖
- 区域逻辑和支付方式测试

**关键测试方法**:
```kotlin
fun `getAvailablePlans should return success when repository succeeds`()
fun `observeCurrentUserSubscriptionStatus should return subscription status`()
fun `getPlanById should return null when plan not found`()
```

#### 3. workoutViewModel (ViewModel层现代化测试案例)
**文件**: `features/workout/src/test/kotlin/.../workoutViewModelTest.kt`

**特点**:
- StateFlow状态变化测试
- 事件处理和倒计时控制
- 使用Turbine测试UI状态流
- Mock配置适配MVVM架构

**关键测试方法**:
```kotlin
fun `workoutViewModel should have correct initial state`()
fun `onEvent should handle countdown actions correctly`()
fun `convenience methods should trigger correct actions`()
```

#### 4. OptimizedSubscriptionViewModel (复杂状态管理测试案例)
**文件**: `features/subscription/src/test/kotlin/.../OptimizedSubscriptionViewModelTest.kt`

**特点**:
- 复杂的UI状态管理
- 区域支付方式逻辑
- 错误处理和恢复机制
- 用户交互事件测试

## 🔄 持续改进和经验总结

### 关键经验总结

1. **模板优先原则**
   - 先制作一个不会出错的genTest模板
   - 然后根据对应的功能做增加删除内容
   - 确保基础模板100%编译通过

2. **分层测试策略**
   - 按模块分别编译和测试
   - Domain层优先，ViewModel层次之
   - 逐步验证，避免大批量错误

3. **Mock配置精确性**
   - 特别注意Flow vs StateFlow的区别
   - MockK类型推断需要明确指定
   - 协程测试使用TestScope和runTest

4. **错误处理全面性**
   - 覆盖各种异常场景
   - 验证ModernResult的正确使用
   - 测试用户认证和权限检查

5. **文档同步更新**
   - 及时记录进度和经验
   - 更新workflow.md状态
   - 归档成功案例供参考

### 模板优化方向

1. **新增模板类型**
   - Repository层测试模板
   - UI组件测试模板
   - 集成测试模板

2. **模板功能增强**
   - 添加更多测试模式
   - 集成新的测试工具
   - 优化Mock配置

3. **流程自动化**
   - 简化重复性工作
   - 提高测试生成效率
   - 完善质量检查机制

### 质量保证措施

1. **编译验证**: 每个测试文件必须100%编译通过
2. **运行验证**: 所有测试必须成功执行
3. **覆盖率验证**: 达到各层级的覆盖率目标
4. **代码审查**: 遵循架构原则和编码规范
5. **文档更新**: 同步更新相关文档和记录

---

## 📋 总结

本测试流程标准规范基于GymBro项目实际测试体系建设经验制定，提供了：

- **完整的测试模板体系** (domain、viewmodel、feature)
- **标准化的4阶段工作流程** (模板验证→生成定制→编译运行→覆盖率验证)
- **详细的最佳实践指南** (Domain、ViewModel、Flow、错误处理)
- **全面的质量检查清单** (编译、功能、规范、架构)
- **成功案例参考** (4个已验证的测试实现)

通过遵循本规范，可以确保测试的高质量、高效率和标准化，为GymBro项目提供稳定可靠的测试保障。

---

**版本**: v1.0
**更新日期**: 2025年1月30日
**基于**: GymBro项目测试体系建设实践经验
**适用范围**: Clean Architecture + MVVM架构的Android项目
