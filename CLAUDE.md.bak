# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 Essential Commands

### Build & Quality Checks
```bash
# Build the application
./gradlew assembleDebug

# Run all quality checks (recommended before commits)
./gradlew qualityCheckAll

# Run tests for all modules
./gradlew testAll

# Format code (Ktlint + auto-fix)
./gradlew formatCodeAll

# CI checks (mimics CI pipeline)
./gradlew ciCheck

# Check test coverage
./gradlew coverageCheck
```

### Single Module Commands
```bash
# Run tests for a specific module
./gradlew :features:coach:testDebugUnitTest

# Quality check for specific module
./gradlew :features:coach:qualityCheck

# Function Call tests (AI Coach feature)
./gradlew :features:coach:testDebugUnitTest --tests "*FunctionCallIntegrationTest*"
```

### Development Workflow
```bash
# Quick quality check (no tests)
./gradlew qualityCheckFast

# Project health check
./gradlew projectHealth

# Update Detekt baseline (when adding justified violations)
./gradlew updateDetektBaselineAll
```

## 🚀 Project Overview

**GymBro** is a modern AI-powered fitness coaching application built with Clean Architecture + MVI 2.0 pattern. It provides personalized workout guidance, AI-driven coaching, and comprehensive fitness tracking with a focus on intelligent user experience.

### Key Features
- **🤖 AI Coach**: Real-time conversational AI using streaming responses
- **🛠️ Function Call System**: ChatGPT-style tool panel with 4 core tools (NEW)
- **🎨 Modern UI**: Jetpack Compose with Material Design 3
- **🏗️ Clean Architecture**: Strict layer separation with MVI 2.0 pattern
- **🔍 Intelligent Search**: Hybrid search combining FTS5 and BGE vector search
- **🎯 Multi-modal Input**: Text, voice, images, and file uploads

## 🏗️ High-Level Architecture

### Core Architectural Principles
- **Clean Architecture**: Strict layer separation with dependency inversion
- **MVI 2.0**: Model-View-Intent pattern with unidirectional data flow
- **Local-First**: Room database as primary data source with offline support
- **AI-Powered**: BGE vector search + streaming AI responses
- **Modern Android**: Jetpack Compose + Material Design 3 + Hilt DI

### Critical Architecture Rules
1. **Layer Dependencies**: Features → Domain → Data → Core (strict upward dependency only)
2. **MVI Flow**: Intent → Reducer → State → Effect → UI (unidirectional data flow)
3. **Dependency Injection**: Use Hilt with interface-based abstractions
4. **State Management**: Single source of truth in ViewModels using StateFlow
5. **Error Handling**: Consistent `Result<T>` wrapper pattern throughout

### Module Structure
```
GymBro/
├── 📱 app/                    # Android application entry point
├── 🔧 core/                   # Core utilities and AI systems (17 sub-packages)
├── 🌐 core-network/           # Network layer with WebSocket support
├── 🌐 core-user-data-center/  # core-user-data-center
├── 🧠 core-ml/                # AI/ML engine (BGE, TensorFlow Lite)
├── 🏛️ core-arch/              # MVI architecture foundation
├── 📋 domain/                 # Business logic layer (37+ UseCases)
├── 💾 data/                   # Repository implementations
├── 🔌 di/                     # Dependency injection (Hilt)
├── 📦 shared-models/          # Cross-module DTOs
├── 🎨 designSystem/           # UI design system
├── 🧭 navigation/             # Navigation management
├── 📂 features/               # Feature modules
│   ├── 👤 profile/            # User profile (API standard)
│   ├── 🤖 coach/              # AI coach (MVI 2.0) + Function Call System
│   ├── 💭 thinkingbox/        # AI thinking visualization
│   ├── 💪 workout/            # Workout tracking
│   ├── 🔐 auth/               # Authentication
│   ├── 🏠 home/               # Home dashboard
│   ├── 💎 subscription/       # Premium features
│   └── 📚 exercise-library/   # Exercise database
├── ⚙️ gradle/build-logic/     # Custom build plugins
└── 📄 scripts/               # Testing and monitoring scripts
```

### Layer Dependencies
```
Presentation (Features) → Domain → Data
         ↓                 ↓        ↓
    Navigation      DI Module   shared-models
         ↓                 ↓        ↓
    designSystem    core-arch    core-network
         ↓                 ↓        ↓
        core           core-ml    (external)
```

## 🚀 Quick Start for New Contributors

### 1. First Time Setup
```bash
# Verify environment
./gradlew projectHealth

# Run full quality check to ensure clean baseline
./gradlew qualityCheckAll
```

### 2. Before Making Changes
```bash
# Always check existing interfaces and documentation
# Key files to check:
# - INTERFACES.md (project-wide interfaces)
# - Module-specific README.md files
# - features/*/docs/ (feature documentation)
```

### 3. Development Workflow
```bash
# 1. Make your changes following MVI 2.0 pattern
# 2. Format code
./gradlew formatCodeAll

# 3. Run quality checks
./gradlew qualityCheckAll

# 4. Run relevant tests
./gradlew testAll
```

### 4. Key Files to Know
- `INTERFACES.md` - All major APIs and interfaces
- `features/*/README.md` - Feature module documentation
- `gradle/build-logic/` - Custom build plugins and quality standards
- `shared-models/` - DTOs shared across modules
- `domain/` - Business logic (37+ UseCases)

## 🔧 MVI 2.0 Pattern (MANDATORY)

Every feature module MUST follow this exact pattern:

```kotlin
// Contract - Define state, intents, and effects
object FeatureContract {
    data class State(val data: List<Item> = emptyList())
    sealed interface Intent { object LoadData : Intent }
    sealed interface Effect { object NavigateBack : Effect }
}

// Reducer - Pure state transformation
class FeatureReducer {
    fun reduce(intent: Intent, state: State): ReduceResult<State, Effect>
}

// EffectHandler - Handle side effects
class FeatureEffectHandler {
    suspend fun handle(intent: Intent, state: State): Flow<Effect>
}

// ViewModel - Coordinate MVI flow
@HiltViewModel
class FeatureViewModel @Inject constructor(
    private val reducer: FeatureReducer,
    private val effectHandler: FeatureEffectHandler
) : ViewModel()
```

### Clean Architecture Layers

1. **Presentation Layer**: UI components, ViewModels, navigation
2. **Domain Layer**: Business logic, use cases, entities
3. **Data Layer**: Repository implementations, data sources

### Dependency Injection
- **Hilt** for dependency injection
- Module-specific DI configurations
- Interface-based abstractions

## ⚡ Non-Negotiable Code Quality Standards

### Before ANY commit:
1. **Zero warnings**: All lint, detekt, and ktlint checks must pass
2. **Test coverage**: Domain ≥90%, Data ≥80%, ViewModel ≥75%
3. **MVI compliance**: All features must follow MVI 2.0 pattern exactly
4. **Interface consistency**: Use existing patterns from `INTERFACES.md`

### Forbidden Practices:
- ❌ Direct Material3 `colorScheme` usage (use Design Tokens)
- ❌ TODO/FIXME comments in committed code
- ❌ Breaking Clean Architecture layer dependencies
- ❌ State management outside ViewModels
- ❌ UI logic in Composables (use proper state hoisting)

### Quality Commands (run before commits):
```bash
./gradlew qualityCheckAll  # Comprehensive check
./gradlew ciCheck         # CI pipeline simulation
./gradlew coverageCheck   # Verify test coverage thresholds
```

## 🛠️ Key Technical Decisions

### AI/ML Integration
- **BGE Vector Search**: Semantic similarity search using BGE-M3 model
- **TensorFlow Lite**: Local model inference for performance
- **Streaming Responses**: Real-time AI chat with WebSocket
- **Token Management**: Intelligent prompt building with budget control

### Function Call System (NEW v2.3)
- **ChatGPT-style Tool Panel**: Bottom slide-up interface with 4 core tools
- **Real-time Detection**: Stream-based Function Call parsing with cross-chunk support
- **Multi-domain Support**: 6 Function Call domains (Exercise, Template, Plan, Session, Nutrition, Training Analysis)
- **Interactive Testing**: Comprehensive testing framework with UI test panels and monitoring scripts

### Data Strategy
- **Local-First**: Room database as primary data source
- **Hybrid Search**: FTS5 + vector search for intelligent content discovery
- **Offline Support**: Core functionality works without internet
- **Premium Sync**: Cloud backup for premium users only

### UI/UX Philosophy
- **Material Design 3**: Modern, accessible design system
- **Compose-First**: All UI built with Jetpack Compose
- **State Hoisting**: Stateless composables with lifted state
- **Accessibility**: Full accessibility support throughout

## 🎯 Function Call System (AI Coach) - Key Feature

### Quick Testing
```bash
# 5-minute comprehensive test
python scripts/run_coach_function_call_tests.py

# Unit tests only
./gradlew :features:coach:testDebugUnitTest --tests "*FunctionCallIntegrationTest*"
```

### Supported Function Calls
- `gymbro.template.generate` - Generate workout templates
- `gymbro.plan.generate_blank` - Create training plans
- `gymbro.nutrition.generate_plan` - Design nutrition plans
- `gymbro.training.analyze` - Analyze training sessions

### Architecture Flow
```
User Input → Tool Panel → Intent.OnToolSelected → Reducer →
Input Prefill → Send → AI Response → Function Call Detection →
Result Dialog Display
```

## 📚 Essential Documentation References

### Project-Wide Documentation
- `INTERFACES.md` - All major APIs and interfaces
- `README.md` - Project overview and latest updates
- `docs/` - Comprehensive project documentation

### Module Documentation (each module has):
- `README.md` - Module overview and usage
- `INTERFACES.md` - Interface definitions
- `build.gradle.kts` - Module-specific build configuration
- `docs/` - Detailed implementation guides

### AI Coach Documentation
- `features/coach/README.md` - Complete Function Call system documentation
- `features/coach/docs/` - Implementation plans and guides

## 🔄 Core Workflow (Chinese Instructions Override)

**核心工作流** (Understand → Ideate → Plan → Execute → Review) 是最主要原则，必须严格遵守。

### **三维评估（必须明确说明）**
对每个任务进行量化评估并在回复开头明确说明：

**评估维度：**
- **理解深度**：需要多少背景信息？
  - 低：需求明确，上下文充足
  - 中：需要部分澄清或背景调研
  - 高：需要深入理解业务逻辑或架构
- **变更范围**：影响代码的广度？
  - 局部：单个方法/函数内修改
  - 模块：单个类/文件或相关文件组
  - 系统：跨模块、架构级别变更
- **风险等级**：出错的影响程度？
  - 低：不影响核心功能，易回滚
  - 中：可能影响部分功能，需要测试
  - 高：可能导致系统故障或数据丢失

### **响应模式选择**

**直接执行模式**（低 + 局部 + 低）
- 适用：明确的bug修复、简单功能添加、代码格式化
- 行动：直接提供解决方案和完整代码

**探索确认模式**（任一维度为中）
- 适用：需要技术选型、部分重构、功能扩展
- 行动：分析问题 → 提供2-3个解决方案 → 使用memory-bank工具确认 → 执行

**协作规划模式**（任一维度为高）
- 适用：架构重构、大规模变更、高风险操作
- 行动：创建工作记录文件 → 分阶段规划 → 逐步执行 → 每阶段确认

## 强制工具使用规范

### 1. 代码库信息检索（必须执行）
- **开始工作前**：调用`get-memory-bank-info`获取项目上下文
- **代码修改前**：使用`codebase-retrieval`查询相关代码结构
- **工作完成后**：调用`update-memory-bank`更新项目记录

### 2. 技术文档查询（编码前必须）
- 使用新库/框架前必须通过`resolve-library-id` + `get-library-docs`查询最新文档
- 禁止基于记忆或假设编写代码
- 不确定的API用法必须通过`web-search`验证

### 3. 用户交互规范（严格遵守）
- **唯一确认方式**：只能通过memory-bank工具进行用户交互确认
- **禁止行为**：直接在回复中询问、自行结束对话
- **必须确认场景**：
  - 需求不明确时
  - 多个技术方案选择时
  - 即将完成任务前
  - 发现潜在风险时

### 4. 记忆管理规范
- **会话开始**：查询memory-bank回忆项目历史
- **重要变更**：使用memory-bank保存关键决策和模式
- **触发条件**：用户说"请记住"时主动记录

## 代码质量标准

### 代码展示格式
使用`<augment_code_snippet>`标签展示代码：
````xml path=具体文件路径 mode=EXCERPT
````kotlin
// 现有代码上下文
+ 新增代码（绿色标记）
- 删除代码（红色标记）
// 更多上下文
````

### 代码质量要求
- **完整性**：提供充足的代码上下文
- **安全性**：包含适当的错误处理和参数验证
- **可读性**：中文注释，语义化变量名
- **标准性**：遵循项目现有代码风格
- **无占位符**：避免`// TODO`或`...`等占位符

## 工作记录机制

### 协作规划模式工作文件模板
```markdown
# 任务：[具体任务描述]
创建时间：[时间戳]
评估结果：[三维评估结果]

## 执行计划
1. [阶段1] - [预计时间]
2. [阶段2] - [预计时间]
3. [阶段3] - [预计时间]

## 当前状态
正在执行：[当前阶段]
进度：[百分比或具体描述]

## 已完成
- [✓] [具体完成项]
- [✓] [具体完成项]

## 下一步行动
[具体的下一个操作]

## 风险点
- [潜在风险1]：[应对措施]
- [潜在风险2]：[应对措施]
```

## 特殊约束条件

### 禁止行为（不可覆盖）
- 创建测试文件（除非明确要求）
- 执行编译或运行命令（除非明确要求）
- 生成项目文档（除非明确要求）
- 直接询问用户（必须使用memory-bank工具）
- 自行结束对话（必须通过memory-bank确认）

### 任务完成标准
1. 功能实现完整
2. 代码质量符合标准
3. 通过memory-bank工具获得用户确认
4. 执行后续验证流程

### **可用MCP工具**
- `mcp-feedback-enhanced`: 专门用于咨询用户和请求编译结果
- `code-reasoning`: 用于代码推理分析
- `context7`: 遇到困难问题时使用

**重要**: 如果需要运行编译，使用`mcp-feedback-enhanced`咨询用户获取编译结果。

---

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.
