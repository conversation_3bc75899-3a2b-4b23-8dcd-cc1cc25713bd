package com.example.gymbro.features.thinkingbox

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import com.example.gymbro.core.ai.tokenizer.TokenizerService
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.thinkingbox.domain.interfaces.UiState
import com.example.gymbro.features.thinkingbox.internal.constants.ThinkingBoxStrings
import com.example.gymbro.features.thinkingbox.internal.presentation.ui.*
import timber.log.Timber
import kotlin.time.Duration

/**
 * ThinkingBoxInternal - 内部实现的思考过程 UI 组件
 *
 * 🎯 **模块分离设计**：
 * - ThinkingBox 内部完整管理所有思考流程，Coach 模块只需导入使用
 * - 完全自主的流程控制：Header显示→思考阶段→完成状态→最终渲染
 * - 内部协调所有动画、时序和用户交互，外部模块无需干预
 * - 实现真正的模块解耦和职责分离
 *
 * 🏗️ **完整流程架构**：
 * - Phase 0: ThinkingHeader（用户发送→立即显示→渐隐消失）
 * - Phase 1: AIThinkingCard（实际思考内容+液态玻璃背景）
 * - Phase 2: SimpleSummaryText（思考完成状态指示器）
 * - Phase 2a: 移除TypewriterRenderer，直接使用渐进式富文本渲染
 * - Phase 3: StreamingFinalRenderer（唯一最终渲染器：流式富文本渲染）
 * - Phase 4: FinalActionsRow（复制按钮和Token计算）
 * - Summary: SummaryCard（用户点击时的摘要面板）
 *
 * 🔄 **内部流程控制**：
 * 1. 接收 UiState 和事件回调
 * 2. 根据状态自主判断各阶段的显示时机
 * 3. 协调动画过渡和用户交互
 * 4. 管理自动滚动和视觉反馈
 * 5. 完成时触发数据持久化
 *
 * @param uiState ThinkingBox 的 UI 状态
 * @param messageId 消息 ID，用于事件发送和状态管理
 * @param tokenizerService Token 计算服务（可选）
 * @param listState LazyColumn 的滚动状态，用于自动滚动控制
 * @param onEventSend 事件发送回调，用于与外部系统通信
 * @param onMessageComplete 消息完成回调，用于历史记录保存
 * @param modifier 修饰符
 */
@Composable
internal fun ThinkingBoxInternal(
    uiState: UiState,
    messageId: String,
    modifier: Modifier = Modifier,
    tokenizerService: TokenizerService? = null,
    listState: LazyListState? = null,
    onEventSend: (
        (
            com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent,
        ) -> Unit
    )? = null,
    onMessageComplete: ((messageId: String, finalMarkdown: String) -> Unit)? = null,
) {
    // 🔥 【状态分析】分析当前的显示状态
    val isCurrentlyStreaming = uiState.isStreaming
    val hasActualThinkingContent = uiState.phases.isNotEmpty() || !uiState.preThinking.isNullOrBlank() // 🔥 【Coach模块控制】重命名以强调实际内容
    val isThinkingCompleted = uiState.isThinkingComplete

    // 🔥 【思考完成状态调试】详细分析思考完成状态
    LaunchedEffect(
        uiState.isThinkingComplete,
        uiState.isStreaming,
        uiState.activePhaseId,
        uiState.phases.size,
    ) {
        Timber.tag("ThinkingBox-ThinkingComplete").e("🚨 [思考完成状态] ===== 思考完成状态分析 =====")
        Timber.tag(
            "ThinkingBox-ThinkingComplete",
        ).e("🚨 [思考完成状态] isThinkingComplete=${uiState.isThinkingComplete}")
        Timber.tag("ThinkingBox-ThinkingComplete").e("🚨 [思考完成状态] isStreaming=${uiState.isStreaming}")
        Timber.tag("ThinkingBox-ThinkingComplete").e("🚨 [思考完成状态] activePhaseId=${uiState.activePhaseId}")
        Timber.tag("ThinkingBox-ThinkingComplete").e("🚨 [思考完成状态] phases总数=${uiState.phases.size}")

        if (uiState.phases.isNotEmpty()) {
            uiState.phases.forEach { phase ->
                Timber.tag(
                    "ThinkingBox-ThinkingComplete",
                ).e(
                    "🚨 [思考完成状态] Phase: ${phase.id}, isComplete=${phase.isComplete}, content长度=${phase.content.length}",
                )
            }
        }

        // 分析为什么思考没有完成
        if (!uiState.isThinkingComplete) {
            Timber.tag("ThinkingBox-ThinkingComplete").e("🚨 [思考完成状态] 思考未完成原因分析:")
            if (uiState.isStreaming) {
                Timber.tag("ThinkingBox-ThinkingComplete").e("🚨 [思考完成状态] - 仍在流式传输中")
            }
            if (uiState.activePhaseId != null) {
                Timber.tag(
                    "ThinkingBox-ThinkingComplete",
                ).e("🚨 [思考完成状态] - 仍有活跃phase: ${uiState.activePhaseId}")
            }
        }
    }

    // 🔥 【重复渲染修复】统一final显示条件，避免重复判断
    val shouldShowFinalText = uiState.finalRichTextReady && (
        uiState.finalTokens.isNotEmpty() || !uiState.finalMarkdown.isNullOrBlank()
        ) // 🔥 【修复】优先使用finalTokens判断（流式渲染），finalMarkdown作为兜底

    // 🔥 【调试增强】实时监控shouldShowFinalText计算结果
    LaunchedEffect(uiState.finalRichTextReady, uiState.finalTokens.size, uiState.finalMarkdown) {
        Timber.tag("ThinkingBox-Calculate").e("🚨 [关键计算] shouldShowFinalText计算过程:")
        Timber.tag("ThinkingBox-Calculate").e("🚨 [关键计算] - finalRichTextReady=${uiState.finalRichTextReady}")
        Timber.tag(
            "ThinkingBox-Calculate",
        ).e("🚨 [关键计算] - finalTokens.isNotEmpty()=${uiState.finalTokens.isNotEmpty()}")
        Timber.tag("ThinkingBox-Calculate").e("🚨 [关键计算] - finalTokens.size=${uiState.finalTokens.size}")
        Timber.tag(
            "ThinkingBox-Calculate",
        ).e("🚨 [关键计算] - finalMarkdown.isNullOrBlank()=${uiState.finalMarkdown.isNullOrBlank()}")
        Timber.tag("ThinkingBox-Calculate").e("🚨 [关键计算] - 最终结果: shouldShowFinalText=$shouldShowFinalText")
        if (uiState.finalTokens.isNotEmpty()) {
            Timber.tag(
                "ThinkingBox-Calculate",
            ).e("🚨 [关键计算] - finalTokens内容: ${uiState.finalTokens.joinToString("").take(100)}...")
        }
    }

    // 🔥 【Summary面板状态】管理摘要面板的显示状态
    var showSummaryPanel by remember { mutableStateOf(false) }

    // 🔥 【SimpleSummaryText状态管理】控制SimpleSummaryText的生命周期
    var summaryTextVisible by remember { mutableStateOf(false) }
    var summaryAnimationComplete by remember { mutableStateOf(false) }

    // 🔥 【数据持久化状态】确保数据只保存一次，避免重复调用
    var hasDataPersisted by remember { mutableStateOf(false) }

    // 🔥 【最终渲染完成状态】控制FinalActionsRow的显示时机
    var isFinalRenderingComplete by remember { mutableStateOf(false) }

    // 🔥 【防重复渲染状态】控制渲染触发状态
    var finalRenderingTriggered by remember { mutableStateOf(false) }

    // 🔥 【移除冗余】移除复杂的防重复渲染机制，StreamingFinalRenderer.kt内部已有防重复逻辑

    // 🔥 【重复渲染修复】原子化数据持久化，防止重复保存和回调
    val dataPersistenceManager = remember(messageId) { // 🔥 【关键修复】绑定messageId，确保每个消息独立管理
        object {
            private var isPersisted = false
            private var isCallbackTriggered = false // 🔥 【新增】防止回调重复触发

            fun persistOnce(messageId: String, finalContent: String): Boolean {
                return if (!isPersisted && !isCallbackTriggered) {
                    isPersisted = true
                    isCallbackTriggered = true
                    onEventSend?.invoke(
                        com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent.FinalAnimationComplete,
                    )
                    onMessageComplete?.invoke(messageId, finalContent)
                    hasDataPersisted = true
                    Timber.tag(
                        "ThinkingBox",
                    ).d("🎯 [原子化持久化] 数据已保存: messageId=$messageId, 内容长度=${finalContent.length}")
                    true
                } else {
                    Timber.tag(
                        "ThinkingBox",
                    ).d(
                        "🎯 [原子化持久化] 数据已保存过，跳过重复保存: isPersisted=$isPersisted, isCallbackTriggered=$isCallbackTriggered",
                    )
                    false
                }
            }

            fun reset() {
                isPersisted = false
                isCallbackTriggered = false
            }
        }
    }

    // 🔥 【持久化状态重置】当开始新的思考流程时重置持久化状态
    LaunchedEffect(messageId) {
        hasDataPersisted = false
        summaryTextVisible = false
        summaryAnimationComplete = false
        isFinalRenderingComplete = false
        finalRenderingTriggered = false // 🔥 【防重复渲染】重置渲染触发状态
        dataPersistenceManager.reset() // 🔥 重置原子化管理器
        Timber.tag("ThinkingBox").d("🔄 [数据持久化] 新消息开始，重置状态: messageId=$messageId")

        // 🔥 【实例唯一性验证】验证ThinkingBox组件唯一性
        Timber.tag("ThinkingBox-Component").w("🚨 [组件实例] ThinkingBox组件激活: messageId=$messageId")
        Timber.tag(
            "ThinkingBox-Component",
        ).w(
            "🚨 [组件实例] UiState检查: isStreaming=${uiState.isStreaming}, phases=${uiState.phases.size}, finalReady=${uiState.finalRichTextReady}",
        )
    }

    // 🔥 【SimpleSummaryText正确时序】仅在所有thinking阶段结束且思考框关闭后激活
    LaunchedEffect(
        isThinkingCompleted,
        uiState.activePhaseId,
        uiState.phases.size,
        shouldShowFinalText,
        messageId,
        uiState.isStreaming,
    ) {
        // 🔥 【根本修复】SimpleSummaryText只有在所有条件都满足时才激活
        // 核心条件：必须等待ThinkingBox内部的思考完成状态确认，不依赖单个phase结束
        val hasNormalPhases = uiState.phases.any { it.id != "perthink" } // 排除perthink

        // 🔥 【关键修复】增加严格的激活条件验证
        val hasValidNormalPhases = hasNormalPhases && uiState.phases.any {
            it.id != "perthink" && it.content.isNotBlank() && it.isComplete // 🔥 必须是完成的phase
        }

        // 🔥 【防止误激活】只有在真正的思考完成且没有活跃phase时才激活
        val shouldActivateSimpleSummary = isThinkingCompleted &&
            uiState.activePhaseId == null &&
            hasValidNormalPhases &&
            !summaryTextVisible &&
            !uiState.isStreaming // 🔥 【固定显示修复】移除与最终文本的冲突条件

        // 🔥 【增强调试】详细记录每个激活条件的状态
        Timber.tag("SimpleSummaryText").e("🚨 [SimpleSummary调试] messageId=$messageId")
        Timber.tag("SimpleSummaryText").e("🚨 [SimpleSummary调试] ===== 激活条件详细分析 =====")
        Timber.tag("SimpleSummaryText").e("🚨 [SimpleSummary调试] 1. isThinkingCompleted=$isThinkingCompleted")
        Timber.tag(
            "SimpleSummaryText",
        ).e("🚨 [SimpleSummary调试] 2. activePhaseId=${uiState.activePhaseId} (需要为null)")
        Timber.tag(
            "SimpleSummaryText",
        ).e("🚨 [SimpleSummary调试] 3. hasValidNormalPhases=$hasValidNormalPhases")
        Timber.tag(
            "SimpleSummaryText",
        ).e("🚨 [SimpleSummary调试] 4. summaryTextVisible=$summaryTextVisible (需要为false)")
        Timber.tag(
            "SimpleSummaryText",
        ).e("🚨 [SimpleSummary调试] 5. isStreaming=${uiState.isStreaming} (需要为false)")
        Timber.tag("SimpleSummaryText").e("🚨 [SimpleSummary调试] ===== 最终结果（固定显示模式）=====")
        Timber.tag(
            "SimpleSummaryText",
        ).e("🚨 [SimpleSummary调试] shouldActivateSimpleSummary=$shouldActivateSimpleSummary")

        // 🔥 【调试增强】详细记录phases内容，便于问题排查
        Timber.tag(
            "SimpleSummaryText",
        ).d(
            "🔍 [严格激活条件] phases内容: ${uiState.phases.joinToString { "${it.id}(${it.content.length}字符,完成:${it.isComplete})" }}",
        )

        if (shouldActivateSimpleSummary) {
            summaryTextVisible = true
            Timber.tag("SimpleSummaryText").i("✅ [修复激活] 思考框完全结束，激活SimpleSummaryText")
            Timber.tag(
                "SimpleSummaryText",
            ).i("🎯 [时序检查] isThinkingCompleted=$isThinkingCompleted, activePhaseId=${uiState.activePhaseId}")
            Timber.tag(
                "SimpleSummaryText",
            ).i("🎯 [时序检查] hasValidNormalPhases=$hasValidNormalPhases, phases数量=${uiState.phases.size}")

            // 🔥 【修复触发时机】在SimpleSummaryText显示后再触发final渲染
            // 🔥 【防重复激活】确保FinalRenderingReady事件只发送一次
            if (!summaryAnimationComplete) {
                onEventSend?.invoke(
                    com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent.FinalRenderingReady,
                )
                Timber.tag("SimpleSummaryText").i("🚀 [修复时序] SimpleSummaryText激活后发送FinalRenderingReady")
            } else {
                Timber.tag("SimpleSummaryText").d("🔒 [防重复] FinalRenderingReady已发送过，跳过重复发送")
            }
        } else {
            // 🔥 【调试增强】记录未激活的原因
            val reasons = mutableListOf<String>()
            if (!isThinkingCompleted) reasons.add("思考未完成")
            if (uiState.activePhaseId != null) reasons.add("仍有活跃phase(${uiState.activePhaseId})")
            if (!hasValidNormalPhases) reasons.add("无有效完成的正式phase")
            if (summaryTextVisible) reasons.add("已经可见")
            if (shouldShowFinalText) reasons.add("final文本冲突")
            if (uiState.isStreaming) reasons.add("仍在流式传输")

            Timber.tag("SimpleSummaryText").d("🚫 [未激活原因] ${reasons.joinToString(", ")}")
        }
    }

    // 🔥 【717修复方案】Final 状态分析，简化为直接显示渐进式富文本
    val hasFinalTokens = uiState.finalTokens.isNotEmpty()
    val isFinalStreaming = uiState.isFinalStreaming

    // 🔥 【调试增强】详细记录final显示条件
    LaunchedEffect(shouldShowFinalText, uiState.finalTokens.size) {
        Timber.tag("ThinkingBox-Final").w("🔍 [Final显示条件] shouldShowFinalText=$shouldShowFinalText")
        Timber.tag("ThinkingBox-Final").w("🔍 [Final显示条件] finalRichTextReady=${uiState.finalRichTextReady}")
        Timber.tag("ThinkingBox-Final").w("🔍 [Final显示条件] finalTokens数量=${uiState.finalTokens.size}")
        Timber.tag(
            "ThinkingBox-Final",
        ).w("🔍 [Final显示条件] finalMarkdown长度=${uiState.finalMarkdown?.length ?: 0}")
        if (uiState.finalTokens.isNotEmpty()) {
            Timber.tag(
                "ThinkingBox-Final",
            ).w("🔍 [Final显示条件] finalTokens内容预览: ${uiState.finalTokens.joinToString("").take(50)}...")
        }
    }

    // 🔥 【717修复方案】添加FinalRenderingReady事件触发逻辑
    // 🔥 【防重复触发】确保只在特定条件下触发，且不与SimpleSummaryText重复
    LaunchedEffect(
        isThinkingCompleted,
        uiState.finalContentArrived,
        uiState.finalRichTextReady,
        summaryTextVisible,
    ) {
        // 当思考完成，并且final内容已到，但UI渲染还未就绪时，发送事件
        // 🔥 【防重复机制】只有在SimpleSummaryText未激活时才发送，避免重复
        if (isThinkingCompleted && uiState.finalContentArrived && !uiState.finalRichTextReady && !summaryTextVisible) {
            Timber.tag("ThinkingBox").i("✅ [UI触发] 条件满足，发送 FinalRenderingReady 事件！")
            Timber.tag("ThinkingBox").i("🔒 [防重复] SimpleSummaryText未激活，直接触发final渲染")
            onEventSend?.invoke(
                com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent.FinalRenderingReady,
            )
        }
    }

    // 🔥 【问题2调试】记录组件状态和final内容
    LaunchedEffect(
        uiState.phases.size,
        uiState.isStreaming,
        uiState.finalRichTextReady,
        uiState.finalMarkdown,
        uiState.finalContentArrived,
    ) {
        Timber.tag("ThinkingBox").i(
            "🎯 [UI状态] 状态更新: messageId=$messageId, " +
                "isStreaming=$isCurrentlyStreaming, hasActualContent=$hasActualThinkingContent, " +
                "isComplete=$isThinkingCompleted, shouldShowFinal=$shouldShowFinalText",
        )
        Timber.tag("ThinkingBox").i(
            "🎯 [UI状态] Final相关: finalRichTextReady=${uiState.finalRichTextReady}, " +
                "finalContentArrived=${uiState.finalContentArrived}, " +
                "finalMarkdown长度=${uiState.finalMarkdown?.length ?: 0}",
        )

        // 🔥 【新增调试】SimpleSummaryText相关状态
        Timber.tag("ThinkingBox").i(
            "🎯 [UI状态] SimpleSummary相关: summaryTextVisible=$summaryTextVisible, " +
                "summaryAnimationComplete=$summaryAnimationComplete, " +
                "showSummaryPanel=$showSummaryPanel",
        )

        // 🔥 【问题2调试】详细分析final显示条件
        if (!shouldShowFinalText) {
            if (!uiState.finalRichTextReady) {
                Timber.tag("ThinkingBox").d("🔍 [Final调试] finalRichTextReady=false，等待激活")
            }
            if (uiState.finalMarkdown.isNullOrBlank()) {
                Timber.tag("ThinkingBox").w("⚠️ [Final调试] finalMarkdown为空或null")
                // 🔥 【增强调试】记录finalMarkdown的具体值
                Timber.tag(
                    "ThinkingBox",
                ).w(
                    "⚠️ [Final调试增强] finalMarkdown实际值='${uiState.finalMarkdown}', finalTokens数量=${uiState.finalTokens.size}",
                )
                if (uiState.finalTokens.isNotEmpty()) {
                    Timber.tag(
                        "ThinkingBox",
                    ).w(
                        "⚠️ [Final调试增强] finalTokens内容预览='${uiState.finalTokens.joinToString(
                            "",
                        ).take(100)}...'",
                    )
                }
            }
        } else {
            Timber.tag("ThinkingBox").i("✅ [UI状态] 准备显示final内容")
            val finalContentPreview = if (!uiState.finalMarkdown.isNullOrBlank()) {
                uiState.finalMarkdown.take(100)
            } else {
                uiState.finalTokens.joinToString("").take(100)
            }
            Timber.tag("ThinkingBox").d("✅ [UI状态] Final内容预览: $finalContentPreview...")
        }
    }

    Column(
        modifier = modifier
            .fillMaxWidth(), // 🔥 【完整流程】ThinkingBox内部完整管理所有流程，Coach模块只需导入使用
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall), // 🔥 【流程协调】统一间距，确保各阶段平滑过渡
    ) {
        // 🔥 【Phase 0】ThinkingHeader - 用户消息发送后立即显示
        // 完整时序：用户发送→立即显示→token解析开始→ThinkingBox加载→Header渐隐消失
        // ThinkingBox内部完整控制此流程，Coach模块无需干预
        val hasPreThinking = !uiState.preThinking.isNullOrBlank()
        val hasPhases = uiState.phases.isNotEmpty()

        // Header显示逻辑：ThinkingBox内部自主判断，无需外部控制
        // 1. 初始状态：用户发送消息后流式状态触发显示
        // 2. PreThinking阶段：显示预思考内容
        // 3. 正式Phase开始：Header自动渐隐消失
        // 4. 完整流程由ThinkingBox内部协调
        val shouldShowThinkingHeader = when {
            hasPhases -> false // 有正式phase时隐藏header
            isThinkingCompleted -> false // 思考完成时隐藏header
            shouldShowFinalText -> false // 显示final时隐藏header
            else -> isCurrentlyStreaming || hasPreThinking // 流式传输中或有预思考时显示
        }

        // 🔧 【调试】ThinkingHeader 显示条件调试
        LaunchedEffect(
            shouldShowThinkingHeader,
            isCurrentlyStreaming,
            hasPreThinking,
            hasPhases,
            isThinkingCompleted,
        ) {
            Timber.tag("ThinkingHeader-Debug").e("🚨 [ThinkingHeader显示] shouldShow=$shouldShowThinkingHeader")
            Timber.tag(
                "ThinkingHeader-Debug",
            ).e("🚨 [ThinkingHeader显示] isCurrentlyStreaming=$isCurrentlyStreaming")
            Timber.tag("ThinkingHeader-Debug").e("🚨 [ThinkingHeader显示] hasPreThinking=$hasPreThinking")
            Timber.tag("ThinkingHeader-Debug").e("🚨 [ThinkingHeader显示] hasPhases=$hasPhases")
            Timber.tag(
                "ThinkingHeader-Debug",
            ).e("🚨 [ThinkingHeader显示] isThinkingCompleted=$isThinkingCompleted")
            Timber.tag(
                "ThinkingHeader-Debug",
            ).e("🚨 [ThinkingHeader显示] shouldShowFinalText=$shouldShowFinalText")
        }

        AnimatedVisibility(
            visible = shouldShowThinkingHeader,
            enter = fadeIn(animationSpec = AnimationEngine.PhaseTransitionSpecs.FADE_IN),
            exit = fadeOut(animationSpec = AnimationEngine.PhaseTransitionSpecs.FADE_OUT),
        ) {
            ThinkingHeader(
                title = if (hasPreThinking) {
                    uiState.preThinking ?: "${ThinkingBoxStrings.PERTHINK_TITLE}..."
                } else {
                    "thinking..."
                },
                isStreaming = isCurrentlyStreaming,
                hasContent = hasActualThinkingContent,
                hasPreThinking = hasPreThinking,
                modifier = Modifier.fillMaxWidth(),
            )
        }
        // 🔥 【Phase 1】AIThinkingCard - 实际思考内容显示，移除额外包裹
        // 直接渲染 AIThinkingCard，让其内部处理显示逻辑和动画
        if (hasActualThinkingContent && !shouldShowFinalText) {
            AIThinkingCard(
                uiState = uiState,
                messageId = messageId,
                modifier = Modifier.fillMaxWidth(),
                onAnimationFinished = { phaseId ->
                    // 🔥 【719施工方案修复】发送正确的 Phase 动画完成事件
                    Timber.tag("ThinkingBox").i("🎯 [关键事件] Phase 动画完成回调触发: phaseId=$phaseId")
                    onEventSend?.invoke(
                        com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent.PhaseAnimFinished(
                            phaseId,
                        ),
                    )
                    Timber.tag("ThinkingBox").i("🎯 [关键事件] PhaseAnimFinished事件已发送: phaseId=$phaseId")
                },
                onSendEvent = { event ->
                    // 🔥 【事件转发】转发内部事件到外部系统
                    onEventSend?.invoke(event)
                    Timber.tag("ThinkingBox").d("🎯 [事件转发] 转发事件: ${event::class.simpleName}")
                },
            )
        }

        // 🔥 【Phase 2】SimpleSummaryText - 思考完成状态指示器（固定显示）
        // 根据 finalmermaid 大纲自动显示，用户可点击查看摘要，完整流程内部管理
        // 🔥 【修复：固定显示】SimpleSummaryText在思考完成后一直显示，不会因为SummaryPanel而消失
        // 顺序：user message → SimpleSummaryText（固定） → StreamingFinalRenderer → FinalActionsRow
        val shouldShowSummaryText = summaryTextVisible // 🔥 【关键修复】移除与SummaryPanel的互斥条件，实现固定显示

        // 🔥 【调试日志】详细记录SimpleSummaryText的显示状态
        LaunchedEffect(shouldShowSummaryText, summaryTextVisible, showSummaryPanel) {
            Timber.tag(
                "ThinkingBox",
            ).w(
                "🚨 [SimpleSummaryText固定显示] shouldShowSummaryText=$shouldShowSummaryText, summaryTextVisible=$summaryTextVisible, showSummaryPanel=$showSummaryPanel",
            )
            if (shouldShowSummaryText) {
                Timber.tag("ThinkingBox").w("🚨 [SimpleSummaryText固定显示] SimpleSummaryText固定显示！")
            } else {
                Timber.tag("ThinkingBox").w("🚨 [SimpleSummaryText固定显示] 不显示SimpleSummaryText - 检查条件")
            }
        }

        AnimatedVisibility(
            visible = shouldShowSummaryText,
            enter = fadeIn(
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing,
                ),
            ) + expandVertically(
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing,
                ),
            ),
            exit = fadeOut(
                animationSpec = tween(
                    durationMillis = 200,
                    easing = FastOutSlowInEasing,
                ),
            ),
        ) {
            // 计算思考耗时
            val thinkingDuration = remember(uiState.thinkingDuration) {
                Duration.parse("${uiState.thinkingDuration}ms")
            }

            // 🔥 【finalmermaid大纲】使用SimpleSummaryText作为思考完成状态指示器
            // 根据文档第31行："点击'已完成思考' → 呈现summarycard"
            SimpleSummaryTextInternal(
                elapsed = thinkingDuration,
                onClick = {
                    // 🔥 【finalmermaid大纲】点击时展开SummaryCard，符合文档第31行规范
                    showSummaryPanel = true
                    onEventSend?.invoke(
                        com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent.ShowSummaryPanel,
                    )
                },
                modifier = Modifier.fillMaxWidth(),
                onAnimationComplete = {
                    // 🔥 【Phase 2 修复】SimpleSummaryText动画完成标记
                    summaryAnimationComplete = true
                    onEventSend?.invoke(
                        com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent.SummaryAnimationComplete,
                    )
                    Timber.tag("ThinkingBox").i("🎯 [SimpleSummaryText] 动画完成")
                },
            )
        }

        // 🔥 【Phase 3】StreamingFinalRenderer - 唯一渲染器，简化渲染逻辑
        // 🔥 【关键修复】移除多重渲染逻辑，直接使用StreamingFinalRenderer.kt作为唯一渲染器
        if (shouldShowFinalText) {
            StreamingFinalRenderer(
                finalTokens = uiState.finalTokens, // 🔥 【流式输入】监听token流
                isFinalStreaming = uiState.isFinalStreaming, // 🔥 【流式状态】流式传输状态
                tokenizerService = tokenizerService,
                onRenderingComplete = {
                    // 🔥 【原子化数据持久化】使用原子化管理器确保数据只保存一次
                    val finalContent = uiState.finalMarkdown ?: uiState.finalTokens.joinToString("")
                    val success = dataPersistenceManager.persistOnce(messageId, finalContent)

                    if (success) {
                        // 🔥 【修复FinalActionsRow时机】只有在成功保存后才标记渲染完成
                        isFinalRenderingComplete = true
                        Timber.tag("ThinkingBox").i("🎯 [FinalActionsRow修复] 最终渲染完成，允许显示FinalActionsRow")
                    }
                },
                modifier = Modifier
                    .fillMaxWidth(),
                // 🔥 【移除card限制】移除padding，直接在画布上渲染，增加展示区域
            )
        }

        // 🔥 【Phase 4】FinalActionsRow - 复制按钮和Token计算
        // 🔥 【修复时机】只有在富文本渲染完成后才激活，不是文本准备好就激活
        val shouldShowFinalActions = shouldShowFinalText &&
            isFinalRenderingComplete && // 🔥 【关键修复】等待StreamingFinalRenderer完成渲染
            (!uiState.finalMarkdown.isNullOrBlank() || uiState.finalTokens.isNotEmpty())

        AnimatedVisibility(
            visible = shouldShowFinalActions,
            enter = fadeIn(
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing,
                ),
            ) + expandVertically(
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing,
                ),
            ),
            exit = fadeOut(
                animationSpec = tween(
                    durationMillis = 200,
                    easing = FastOutSlowInEasing,
                ),
            ),
        ) {
            FinalActionsRow(
                finalContent = uiState.finalMarkdown ?: uiState.finalTokens.joinToString(""),
                tokenizerService = tokenizerService,
                modifier = Modifier.fillMaxWidth(),
            )
        }

        // 🔥 【Summary面板】完整的摘要显示面板
        // ThinkingBox内部管理Summary面板的显示状态，支持用户交互
        if (showSummaryPanel) {
            SummaryCard(
                uiState = uiState,
                isExpanded = showSummaryPanel,
                onToggle = {
                    showSummaryPanel = false
                },
                modifier = Modifier.fillMaxSize(),
            )
        }
    }

    // 🔥 【智能自动滚动】ThinkingBox内部管理滚动逻辑，确保最佳观看体验
    // 🔥 【反序布局兼容】Coach screen使用reverseLayout=true，需要适配滚动逻辑
    LaunchedEffect(shouldShowFinalText, uiState.activePhaseId) {
        listState?.let { state ->
            if (shouldShowFinalText || uiState.activePhaseId != null) {
                try {
                    // 🔥 【关键修复】适配reverseLayout=true的滚动位置计算
                    // 在反序布局中，index=0是最新内容，index越大越靠上
                    // 我们希望ThinkingBox在屏幕中央偏下位置，便于用户查看
                    val totalItems = state.layoutInfo.totalItemsCount
                    val targetIndex = when {
                        totalItems <= 3 -> 0 // 少量内容时滚动到最新位置
                        else -> minOf(2, totalItems - 1) // 在反序布局中，较小的index更靠下
                    }

                    state.animateScrollToItem(
                        index = targetIndex,
                        scrollOffset = -Tokens.Spacing.Large.value.toInt(), // 🔥 适度偏移，避免贴边
                    )
                    Timber.tag("ThinkingBox").d("🎯 [反序滚动] 滚动到位置: index=$targetIndex, totalItems=$totalItems")
                } catch (e: Exception) {
                    Timber.tag("ThinkingBox").w(e, "🎯 [反序滚动] 滚动失败")
                }
            }
        }
    }
}

// ==================== Preview Functions ====================

@Composable
private fun createMockUiState(
    isStreaming: Boolean = false,
    isThinkingComplete: Boolean = false,
    finalRichTextReady: Boolean = false,
    phases: List<com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi> = emptyList(),
    finalTokens: List<String> = emptyList(),
    finalMarkdown: String? = null,
): UiState {
    return UiState(
        isStreaming = isStreaming,
        preThinking = "Bro 正在思考你的问题...",
        phases = phases,
        activePhaseId = if (phases.isNotEmpty() && !isThinkingComplete) phases.last().id else null,
        isThinkingComplete = isThinkingComplete,
        finalContentArrived = finalTokens.isNotEmpty() || !finalMarkdown.isNullOrBlank(),
        finalRichTextReady = finalRichTextReady,
        finalTokens = finalTokens,
        finalMarkdown = finalMarkdown,
        isFinalStreaming = false,
        isConversationComplete = isThinkingComplete && finalRichTextReady,
        thinkingDuration = 5000L,
    )
}

@Composable
private fun createMockPhase(
    id: String,
    title: String,
    content: String,
    isComplete: Boolean = true,
): com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi {
    return com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi(
        id = id,
        title = title,
        content = content,
        isComplete = isComplete,
    )
}

/**
 * ThinkingBoxInternal Preview - 初始加载状态
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun ThinkingBoxInternalPreview_Loading() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        ThinkingBoxInternal(
            uiState = createMockUiState(
                isStreaming = true,
            ),
            messageId = "preview-loading",
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}

/**
 * ThinkingBoxInternal Preview - 思考阶段
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun ThinkingBoxInternalPreview_Thinking() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        ThinkingBoxInternal(
            uiState = createMockUiState(
                isStreaming = true,
                phases = listOf(
                    createMockPhase(
                        id = "analyze",
                        title = "分析问题",
                        content = "我需要仔细分析你提出的健身计划问题，考虑你的目标、经验水平和可用时间。",
                        isComplete = false,
                    ),
                ),
            ),
            messageId = "preview-thinking",
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}

/**
 * ThinkingBoxInternal Preview - 思考完成状态
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun ThinkingBoxInternalPreview_Complete() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        ThinkingBoxInternal(
            uiState = createMockUiState(
                isThinkingComplete = true,
                phases = listOf(
                    createMockPhase(
                        id = "analyze",
                        title = "分析问题",
                        content = "已完成对你的健身需求的分析。",
                    ),
                    createMockPhase(
                        id = "plan",
                        title = "制定方案",
                        content = "基于分析结果，我为你制定了个性化的训练计划。",
                    ),
                ),
            ),
            messageId = "preview-complete",
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}

/**
 * ThinkingBoxInternal Preview - 最终渲染状态
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun ThinkingBoxInternalPreview_Final() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        ThinkingBoxInternal(
            uiState = createMockUiState(
                isThinkingComplete = true,
                finalRichTextReady = true,
                finalTokens = listOf("根据", "你的", "需求，", "我", "推荐", "以下", "训练", "计划：", "\n\n", "**", "周一", "**：", "胸部", "训练", "\n", "- ", "卧推", " 4", "组", " x ", "8-12", "次"),
                finalMarkdown = "根据你的需求，我推荐以下训练计划：\n\n**周一**：胸部训练\n- 卧推 4组 x 8-12次",
            ),
            messageId = "preview-final",
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}

/**
 * ThinkingBoxCompleteInternal Preview
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun ThinkingBoxCompleteInternalPreview() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        ThinkingBoxCompleteInternal(
            uiState = createMockUiState(
                isThinkingComplete = true,
                finalRichTextReady = true,
                finalTokens = listOf("这是", "一个", "完整的", "ThinkingBox", "预览", "示例。"),
            ),
            messageId = "preview-complete-internal",
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}

/**
 * 判断是否应该显示 ThinkingBox 组件（内部实现）
 *
 * @param uiState ThinkingBox 的 UI 状态
 * @return 是否应该显示组件
 */
internal fun shouldShowThinkingBoxInternal(uiState: UiState): Boolean {
    val hasContent = uiState.phases.isNotEmpty() ||
        !uiState.preThinking.isNullOrBlank() ||
        !uiState.finalMarkdown.isNullOrBlank()

    val isActive = uiState.isStreaming ||
        uiState.isThinkingComplete ||
        uiState.finalRichTextReady

    // 🔧 【修复 ThinkingHeader 显示】当正在流式传输时，即使没有内容也应该显示（用于显示 ThinkingHeader）
    return if (uiState.isStreaming && !hasContent) {
        true // 流式传输开始时显示 ThinkingHeader
    } else {
        hasContent && isActive // 有内容时的正常逻辑
    }
}

/**
 * ThinkingBoxCompleteInternal - 完整的思考过程组件（包含自动滚动和用户交互）
 *
 * 🎯 **高级功能**：
 * - 智能自动滚动：打字机渲染时自动跟踪，保持富文本内容在屏幕中心
 * - 用户交互检测：用户点击任意区域时停止自动滚动
 * - 一键到底部按钮：在输入框上方显示，智能检测用户滚动位置
 * - 完整的生命周期管理：从思考开始到最终渲染完成的全流程控制
 *
 * @param uiState ThinkingBox 的 UI 状态
 * @param messageId 消息 ID
 * @param tokenizerService Token 计算服务
 * @param listState LazyColumn 的滚动状态
 * @param onEventSend 事件发送回调
 * @param onMessageComplete 消息完成回调
 * @param onScrollToBottom 滚动到底部回调
 * @param modifier 修饰符
 */
@Composable
internal fun ThinkingBoxCompleteInternal(
    uiState: UiState,
    messageId: String,
    modifier: Modifier = Modifier,
    tokenizerService: TokenizerService? = null,
    listState: LazyListState? = null,
    onEventSend: (
        (
            com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent,
        ) -> Unit
    )? = null,
    onMessageComplete: ((messageId: String, finalMarkdown: String) -> Unit)? = null,
    onScrollToBottom: (() -> Unit)? = null,
) {
    // 🔥 【v2方案】创建AutoScrollManager和协程作用域
    val actualListState = listState ?: rememberLazyListState()
    val autoScrollManager = rememberAutoScrollManager(actualListState)
    val scope = rememberCoroutineScope()

    // 🔥 【v2方案】Final流式传输完成状态
    val isFinalStreamingFinished = !uiState.isFinalStreaming && uiState.finalTokens.isNotEmpty()

    // 🔥 【合并自动滚动逻辑】统一处理所有自动滚动触发条件，避免重复LaunchedEffect
    LaunchedEffect(uiState.finalTokens.size, uiState.activePhaseId, uiState.isConversationComplete) {
        // 🔥 【渐进式富文本渲染时的自动滚动】
        if (uiState.finalTokens.isNotEmpty()) {
            autoScrollManager.scrollToBottomIfNeeded(scope)
            Timber.tag("ThinkingBox").d("🎯 [自动滚动统一] Final tokens渲染触发滚动: tokens=${uiState.finalTokens.size}")
        }

        // 🔥 【Phase切换时的自动滚动】
        if (uiState.activePhaseId != null) {
            autoScrollManager.scrollToBottomIfNeeded(scope)
            Timber.tag("ThinkingBox").d("🎯 [自动滚动统一] Phase切换触发滚动: activePhaseId=${uiState.activePhaseId}")
        }

        // 🔥 【渲染完成时重置AutoScrollManager】
        if (uiState.isConversationComplete) {
            autoScrollManager.resetAutoMode()
            Timber.tag("ThinkingBox").d("🎯 [自动滚动统一] 富文本渲染完成，重置自动滚动")
        }
    }

    // 🔥 【v2方案】Box布局包含ThinkingBox和ScrollToBottomBtn
    Box(modifier = modifier) {
        // 基础的 ThinkingBox 组件，添加用户交互检测
        ThinkingBoxInternal(
            uiState = uiState,
            messageId = messageId,
            modifier = Modifier.detectUserInteraction(autoScrollManager),
            tokenizerService = tokenizerService,
            listState = actualListState,
            onEventSend = onEventSend,
            onMessageComplete = { msgId, markdown ->
                // 🔥 【完成处理】消息完成时的额外处理
                onMessageComplete?.invoke(msgId, markdown)
                autoScrollManager.resetAutoMode()
                Timber.tag("ThinkingBox").d("🎯 [完成处理] 消息完成，重置自动滚动: messageId=$msgId")
            },
        )

        // 🔥 【v2方案】ScrollToBottomBtn覆盖层
        ScrollToBottomBtnPositioned(
            autoScrollManager = autoScrollManager,
            isFinalStreamingFinished = isFinalStreamingFinished,
            scope = scope,
            bottomPadding = Tokens.Spacing.Huge, // 🔥 【间距统一】使用Huge(64dp)作为底部间距，避免与输入栏重叠
        )
    }
}
