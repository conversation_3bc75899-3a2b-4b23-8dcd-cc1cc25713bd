package com.example.gymbro.features.thinkingbox.internal.presentation.ui

import androidx.compose.animation.*
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi
import com.example.gymbro.features.thinkingbox.internal.constants.ThinkingBoxStrings
import timber.log.Timber

/**
 * AIThinkingCard - 统一架构实现
 * 🔥 【Coach模块控制】现在专注于实际思考内容显示，ThinkingHeader由Coach模块控制
 */
fun shouldShowAIThinkingCard(
    uiState: com.example.gymbro.features.thinkingbox.domain.interfaces.UiState,
): Bo<PERSON>an {
    return when {
        // 🔥 【实际内容优先】只有当有实际的思考内容时才显示AIThinkingCard
        uiState.activePhaseId != null -> true
        // 如果有未完成的阶段，继续显示
        uiState.phases.any { !it.isComplete } -> true
        // 如果有预思考内容，显示
        uiState.preThinking != null -> true
        // 🔥 【Coach模块控制】移除纯流式传输状态的显示条件
        // 用户发送消息后的"thinking"状态由Coach模块的ThinkingHeader处理
        // 其他情况隐藏
        else -> false
    }
}

// 🔥 【重载冲突修复】SimpleSummaryText已移至ThinkingBoxExports.kt作为公共API
// 内部实现在SimpleSummaryTextInternal.kt中，避免重复定义

/**
 * 🔥 【Phase 切换动画重构】AIThinkingCard - 集成 Phase 切换动画
 *
 * 核心变更：
 * - 移除对 Coach 模块状态的依赖
 * - 直接使用 ThinkingBox UiState 进行渲染
 * - 实现完全解耦的事件驱动架构
 * - 🆕 集成 Phase 切换的渐隐渐显动画效果
 * - 🆕 支持双时序架构的平滑过渡
 *
 * @param uiState ThinkingBox UiState
 * @param messageId 消息ID
 * @param modifier 修饰符
 * @param onAnimationFinished 动画完成回调
 */
@Composable
fun AIThinkingCard(
    uiState: com.example.gymbro.features.thinkingbox.domain.interfaces.UiState,
    messageId: String,
    modifier: Modifier = Modifier,
    onAnimationFinished: ((String) -> Unit)? = null,
    // 🔥 【思考完成UI修复】添加事件发送回调，用于发送摘要完成事件
    onSendEvent: (
        (
            com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent,
        ) -> Unit
    )? = null,
) {
    // 🔥 【修复30】添加UI状态监听，确保phase切换时UI重组
    val currentActivePhaseId: String? by remember { derivedStateOf { uiState.activePhaseId } }
    val currentPhases: List<PhaseUi> by remember { derivedStateOf { uiState.phases } }
    val currentIsStreaming: Boolean by remember { derivedStateOf { uiState.isStreaming } }

    // 🔥 【修复30】强制UI重组的关键：监听状态变化
    LaunchedEffect(currentActivePhaseId, currentPhases.size, currentIsStreaming) {
        Timber.tag(
            "TB-UI",
        ).d(
            "🔄 [UI重组] activePhaseId=$currentActivePhaseId, phases=${currentPhases.map { it.id }}, isStreaming=$currentIsStreaming",
        )
    }

    val phaseToRender = findPhaseToRender(uiState)

    // 🔥 【修复24】直接使用原始回调，避免复杂的状态管理
    val enhancedAnimationFinished: (String) -> Unit = remember(onAnimationFinished) {
        {
                phaseId ->
            Timber.tag("TB-ANIM").d("🎬 Phase $phaseId 动画完成")
            onAnimationFinished?.invoke(phaseId)
        }
    }

    // 🚀 【简化动画设计】移除双重动画，只保留核心的phase切换
    when {
        phaseToRender != null -> {
            Timber.tag("TB-UI").d("🎯 [渲染] 显示phase: ${phaseToRender.id}, activePhaseId=$currentActivePhaseId")

            // 🚀 【简化结构】直接渲染ThinkingStageCard，动画交给内部处理
            ThinkingStageCard(
                phase = phaseToRender,
                isPreThink = phaseToRender.id == "perthink", // 🔥 【修复perthink判断】精确匹配perthink id
                isActive = phaseToRender.id == currentActivePhaseId,
                modifier = modifier.fillMaxWidth(),
                onAnimationFinished = enhancedAnimationFinished,
            )
        }
        else -> {
            // 🔥 【Coach模块控制】ThinkingHeader的显示逻辑移至Coach模块
            // AIThinkingCard只负责实际的思考内容显示
            Timber.tag("TB-UI").d("🎯 [渲染] 无phase内容显示，等待Coach模块的ThinkingHeader控制")
        }
    }
}

/**
 * 辅助函数：从 ThinkingBox UiState 中找到要渲染的 Phase
 *
 * 🔥 【架构重构】将原有的复杂 phase 查找逻辑提取为独立函数
 */
private fun findPhaseToRender(
    uiState: com.example.gymbro.features.thinkingbox.domain.interfaces.UiState,
): PhaseUi? {
    Timber.tag("TB-UI").d("🔍 [findPhaseToRender] activePhaseId=${uiState.activePhaseId}, phases=${uiState.phases.map { "${it.id}(${it.isComplete})" }}")
    Timber.tag("TB-UI").d("🔍 [findPhaseToRender] preThinking存在=${uiState.preThinking != null}, perthinkCompleted=${uiState.perthinkCompleted}")
    Timber.tag("TB-UI").d("🔍 [findPhaseToRender] isStreaming=${uiState.isStreaming}")

    // 🔥 【perthink调试】特别检查perthink相关状态
    if (uiState.activePhaseId == "perthink" || uiState.phases.any { it.id == "perthink" } || uiState.preThinking != null) {
        Timber.tag("TB-UI").w("🚨 [perthink调试] 检测到perthink相关状态:")
        Timber.tag("TB-UI").w("🚨 [perthink调试] → activePhaseId=${uiState.activePhaseId}")
        Timber.tag("TB-UI").w("🚨 [perthink调试] → phases中有perthink=${uiState.phases.any { it.id == "perthink" }}")
        Timber.tag("TB-UI").w("🚨 [perthink调试] → preThinking内容长度=${uiState.preThinking?.length ?: 0}")
        Timber.tag("TB-UI").w("🚨 [perthink调试] → perthinkCompleted=${uiState.perthinkCompleted}")
    }

    return when {
        // 第一优先级：activePhaseId对应的phase（遵循双时序架构）
        uiState.activePhaseId != null -> {
            // 🔥 【perthink特殊处理】如果activePhaseId是perthink，优先从phases中查找，如果不存在则使用preThinking创建临时phase
            if (uiState.activePhaseId == "perthink") {
                val perthinkPhase = uiState.phases.find { it.id == uiState.activePhaseId }
                if (perthinkPhase != null) {
                    Timber.tag("TB-UI").d("🎯 [findPhaseToRender] 找到perthink phase: title='${perthinkPhase.title}', content长度=${perthinkPhase.content.length}")
                    perthinkPhase
                } else if (uiState.preThinking != null) {
                    // 兜底：使用preThinking创建临时phase
                    val tempPhase = PhaseUi("perthink", ThinkingBoxStrings.PERTHINK_TITLE, uiState.preThinking!!)
                    Timber.tag("TB-UI").d("🎯 [findPhaseToRender] 创建preThinking临时phase: content长度=${uiState.preThinking!!.length}")
                    tempPhase
                } else {
                    Timber.tag("TB-UI").w("⚠️ [findPhaseToRender] activePhaseId=perthink 但无对应phase和preThinking")
                    null
                }
            } else {
                // 🔥 【修复30】正常的activePhaseId处理，增强日志
                val activePhase = uiState.phases.find { it.id == uiState.activePhaseId }
                if (activePhase != null) {
                    Timber.tag("TB-UI").d("🎯 [findPhaseToRender] 找到activePhase: ${activePhase.id}, title='${activePhase.title}', content长度=${activePhase.content.length}, isComplete=${activePhase.isComplete}")
                    activePhase
                } else {
                    Timber.tag("TB-UI").e("❌ [findPhaseToRender] activePhaseId ${uiState.activePhaseId} 在phases中未找到！可用phases: ${uiState.phases.map { it.id }}")
                    null
                }
            }
        }

        // 第二优先级：preThinking存在且perthink未完成时，创建临时perthink PhaseUi
        uiState.preThinking != null && !uiState.perthinkCompleted -> {
            val tempPhase = PhaseUi("perthink", ThinkingBoxStrings.PERTHINK_TITLE, uiState.preThinking!!)
            Timber.tag("TB-UI").d("🎯 [问题1修复] 显示preThinking临时phase: content长度=${uiState.preThinking!!.length}")
            tempPhase
        }

        // 🔥 【问题1调试】检查perthink已完成但仍有preThinking的情况
        uiState.preThinking != null && uiState.perthinkCompleted -> {
            Timber.tag("TB-UI").w("⚠️ [问题1调试] perthink已完成但仍有preThinking内容，跳过显示: preThinking长度=${uiState.preThinking!!.length}")
            null
        }

        // 第三优先级：显示第一个未完成的phase
        uiState.phases.any { !it.isComplete } -> {
            val incompletePhase = uiState.phases.first { !it.isComplete }
            Timber.tag("TB-UI").d("🎯 显示未完成phase: ${incompletePhase.id}, title='${incompletePhase.title}', content长度=${incompletePhase.content.length}")
            incompletePhase
        }

        // 第四优先级：显示最后一个phase
        uiState.phases.isNotEmpty() -> {
            val lastPhase = uiState.phases.last()
            Timber.tag("TB-UI").d("🎯 显示最后phase: ${lastPhase.id}, title='${lastPhase.title}', content长度=${lastPhase.content.length}")
            lastPhase
        }

        else -> {
            Timber.tag("TB-UI").d("🎯 无phase需要显示")
            null
        }
    }
}

// ✅ 【701finalmermaid修复】移除ThinkingCompletedButton和formatDuration
// 完成状态的UI应由外部的 AIFinalBubble 和 SummaryCard 组合负责
// AIThinkingCard 只负责"思考中"的UI，保持职责单一
