package com.example.gymbro.domain.workout.model.template

import com.example.gymbro.shared.models.workout.*

/**
 * WorkoutTemplate转换扩展函数
 *
 * Phase1: 数据类型不匹配修复
 * 实现Domain模型到DTO的转换，支持Function Call兼容性
 */

/**
 * WorkoutTemplate (Domain) → WorkoutTemplateDto
 * 按照文档要求，UseCase返回DTO而不是Domain模型
 */
fun WorkoutTemplate.toDto(): WorkoutTemplateDto =
    WorkoutTemplateDto(
        id = this.id,
        name = this.name,
        description = this.description ?: "",
        difficulty = mapDifficultyToDto(this.difficulty),
        category = mapCategoryToDto(this.category),
        exercises = this.exercises.map { it.toDto() },
        source = TemplateSource.USER, // 默认用户创建
        createdAt = this.createdAt,
        updatedAt = this.updatedAt,
        version = 1, // 默认版本
        // === Phase1版本控制字段 (Function Call兼容性) ===
        isDraft = this.isDraft, // 🔥 修复：直接使用Domain模型的isDraft字段
        currentVersion = this.currentVersion, // 🔥 修复：使用Domain模型的版本号
        isPublished = this.isPublished, // 🔥 修复：使用Domain模型的发布状态
        lastPublishedAt = this.lastPublishedAt, // 🔥 修复：使用Domain模型的发布时间
    )

/**
 * WorkoutTemplateDto → WorkoutTemplate (Domain)
 * 支持从DTO恢复Domain模型
 * @param userId 用户ID，用于正确的数据关联
 */
fun WorkoutTemplateDto.toDomain(userId: String? = null): WorkoutTemplate =
    WorkoutTemplate(
        id = this.id,
        name = this.name.takeIf { it.isNotBlank() } ?: "未命名训练", // 🔥 修复：防止空名称导致崩溃
        description = this.description.takeIf { it.isNotBlank() },
        userId = userId ?: "system", // 🔥 修复：使用传入的用户ID，fallback到system
        exercises = this.exercises.map { it.toDomain() },
        targetMuscleGroups = extractTargetMuscleGroups(this.exercises),
        estimatedDuration = calculateEstimatedDuration(this.exercises),
        difficulty = mapDifficultyFromDto(this.difficulty),
        tags = emptyList(), // 从exercises中提取或使用默认值
        imageUrl = null,
        isFavorite = false, // 默认值
        isPublic = this.actualIsPublished, // 使用兼容性属性
        usageCount = 0, // 默认值
        createdAt = this.createdAt,
        updatedAt = this.updatedAt,
        category = this.category.name,
        lastUsedDate = null,
    )

/**
 * TemplateExercise (Domain) → TemplateExerciseDto
 * 🔥 修复：正确提取 customSets 数据，避免数据丢失
 */
fun TemplateExercise.toDto(): TemplateExerciseDto {
    // 🔥 关键修复：首先尝试从 notes 字段提取现有的 customSets 数据
    val actualCustomSets = try {
        if (!this.notes.isNullOrBlank()) {
            // 使用与其他转换器相同的提取逻辑
            val customSetsMarker = "__CUSTOM_SETS_JSON__:"
            val markerIndex = this.notes.indexOf(customSetsMarker)

            if (markerIndex != -1) {
                val jsonStartIndex = markerIndex + customSetsMarker.length
                val customSetsJson = this.notes.substring(jsonStartIndex)

                // 解析 JSON 数据
                val json = kotlinx.serialization.json.Json {
                    ignoreUnknownKeys = true
                    encodeDefaults = true
                    isLenient = true
                }

                val parsedCustomSets = json.decodeFromString<List<com.example.gymbro.shared.models.workout.TemplateSetDto>>(customSetsJson)

                if (parsedCustomSets.isNotEmpty()) {
                    // 🔥 [DOMAIN-TO-DTO] 成功提取 customSets: ${this.name}, 组数=${parsedCustomSets.size}
                    parsedCustomSets
                } else {
                    null // 解析结果为空，使用默认值
                }
            } else {
                null // 没有找到标记，使用默认值
            }
        } else {
            null // notes 为空，使用默认值
        }
    } catch (e: Exception) {
        // 🔥 [DOMAIN-TO-DTO] 提取 customSets 失败，使用默认值: ${this.name}
        null
    }

    // 🔥 修复：只有在无法提取现有数据时才创建默认 customSets
    val finalCustomSets = actualCustomSets ?: run {
        // 🔥 [DOMAIN-TO-DTO] 创建默认 customSets: ${this.name}, 组数=${this.sets}
        (1..this.sets).map { setNumber ->
            com.example.gymbro.shared.models.workout.TemplateSetDto(
                setNumber = setNumber,
                targetWeight = this.weight ?: 0f,
                targetReps = this.reps,
                restTimeSeconds = this.restSeconds,
                targetDuration = null,
                rpe = null
            )
        }
    }

    return TemplateExerciseDto(
        id = this.id,
        exerciseId = this.exerciseId,
        exerciseName = this.name,
        sets = this.sets,
        reps = this.reps,
        rpe = null, // 默认值，需要后续扩展
        targetWeight = this.weight,
        restTimeSeconds = this.restSeconds,
        customSets = finalCustomSets // 🔥 关键修复：使用提取的或默认的 customSets
    )
}

/**
 * TemplateExerciseDto → TemplateExercise (Domain)
 * 🔥 修复：保持基础字段独立，不被 customSets 覆盖
 */
fun TemplateExerciseDto.toDomain(): TemplateExercise {
    // 🔥 关键修复：优先使用 customSets 数据，确保 sets = customSets.length
    val effectiveSets = if (this.customSets.isNotEmpty()) {
        this.customSets.size
    } else {
        this.sets
    }

    // 🔥 关键修复：构建包含 customSets 的 notes 字段，确保数据不丢失
    val notesWithCustomSets = if (this.customSets.isNotEmpty()) {
        try {
            val customSetsJson = kotlinx.serialization.json.Json.encodeToString(this.customSets)
            val actualNotes = this.notes?.takeIf { it.isNotBlank() }

            // 🔥 调试日志：验证序列化结果
            val finalNotes = if (actualNotes != null) {
                "$actualNotes\n__CUSTOM_SETS_JSON__:$customSetsJson"
            } else {
                "__CUSTOM_SETS_JSON__:$customSetsJson"
            }
            
            // 🔥 数据完整性验证：确保生成的notes包含正确的标记格式
            if (!finalNotes.contains("__CUSTOM_SETS_JSON__:")) {
                throw Exception("生成的notes缺少正确的标记格式")
            }
            
            finalNotes
        } catch (e: Exception) {
            // 序列化失败时的日志记录（domain 层不依赖 Timber）
            this.notes
        }
    } else {
        this.notes
    }

    return TemplateExercise(
        id = this.id ?: java.util.UUID.randomUUID().toString(),
        exerciseId = this.exerciseId,
        name = this.exerciseName ?: "Unknown Exercise",
        order = 0, // 默认值
        sets = effectiveSets, // 🔥 关键：使用 customSets 数量
        reps = this.reps, // 🔥 修复：使用原有基础字段
        restSeconds = this.restTimeSeconds, // 🔥 修复：使用原有基础字段
        weight = this.targetWeight, // 🔥 修复：使用原有基础字段
        notes = notesWithCustomSets // 🔥 关键修复：保留包含 customSets 的 notes 数据
    )
}

// === 辅助映射函数 ===

/**
 * 将Domain difficulty映射到DTO difficulty
 */
private fun mapDifficultyToDto(difficulty: Int?): Difficulty =
    when (difficulty) {
        1 -> Difficulty.EASY
        2 -> Difficulty.EASY
        3 -> Difficulty.MEDIUM
        4 -> Difficulty.HARD
        5 -> Difficulty.EXPERT
        else -> Difficulty.MEDIUM
    }

/**
 * 将DTO difficulty映射到Domain difficulty
 */
private fun mapDifficultyFromDto(difficulty: Difficulty): Int =
    when (difficulty) {
        Difficulty.EASY -> 2
        Difficulty.MEDIUM -> 3
        Difficulty.HARD -> 4
        Difficulty.EXPERT -> 5
    }

/**
 * 将Domain category映射到DTO category
 */
private fun mapCategoryToDto(category: String?): TemplateCategory =
    when (category?.lowercase()) {
        "strength" -> TemplateCategory.STRENGTH
        "cardio" -> TemplateCategory.CARDIO
        "flexibility" -> TemplateCategory.FLEXIBILITY
        "mobility" -> TemplateCategory.FLEXIBILITY
        else -> TemplateCategory.STRENGTH
    }

/**
 * 从exercises中提取目标肌群
 */
private fun extractTargetMuscleGroups(exercises: List<TemplateExerciseDto>): List<String> {
    // 简化实现，后续可以从Exercise服务获取详细信息
    return exercises
        .mapNotNull {
            it.exerciseName?.let { name ->
                when {
                    name.contains("胸", ignoreCase = true) -> "胸部"
                    name.contains("背", ignoreCase = true) -> "背部"
                    name.contains("腿", ignoreCase = true) -> "腿部"
                    name.contains("肩", ignoreCase = true) -> "肩部"
                    name.contains("臂", ignoreCase = true) -> "手臂"
                    else -> null
                }
            }
        }.distinct()
}

/**
 * 计算预估训练时长
 */
private fun calculateEstimatedDuration(exercises: List<TemplateExerciseDto>): Int {
    if (exercises.isEmpty()) return 0

    // 简化计算：每个动作平均5分钟
    val exerciseTime = exercises.size * 5
    // 加上热身和整理时间
    return exerciseTime + 15
}
