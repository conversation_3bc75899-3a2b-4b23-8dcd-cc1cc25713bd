
第二个大问题点
phase完成后转换到最终富文本
我们现在的设计
<think>标签=perthink
<thinking>=正式phase
<phase id="...">控制phase刷新
<title>标题</title>=标题
文本内容
</phase>
唯一整个循环
在mermaid需求文档都有记录
</thinking>赋予=phase id="final"---
最后一个phase结束传递给"final"标签传递给思考框关闭渐隐思考框---simplesummarytext+final富文本开始渲染
<final>只会在后台开始渲染.开始缓存渲染内容,所有富文本markdown,mermaid语法都在这时候缓存好后进行流式渲染.
</final>标签不做任何记录.

当phase id="final"后后面的标签就不需要做任何的时序传递了.
后面的内容全部进入缓存+后台渲染
当phase id="final"激活后准备开始最终富文本的渲染直到UI结束
期间使用@/features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/presentation/ui/AutoScrollManager.kt结束渲染后出现@/features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/presentation/ui/FinalActionsRow.kt
-----------

请按照以下具体要求修复 ThinkingBox 模块的时序问题和 UI 组件，确保严格遵循双时序架构：

## 核心问题修复

### 1. 时序架构修复
- **参考基准**: 使用 `git 59b153ae` 作为正确时序匹配的技术参考
- **双时序要求**: 严格匹配双时序架构，确保数据时序与渲染时序正确协调
- **ThinkingStageCard 稳定性**: 修复当前不稳定的时序判断逻辑，确保动画回调参数正确

### 2. XML 标签处理流程（严格按照 finalmermaid大纲.md）
```
<think>标签 → perthink 阶段
<thinking> → 正式 phase 开始
<phase id="..."> → 控制 phase 刷新
  <title>标题</title> → 标题更新
  文本内容 → 内容流式显示
</phase> → phase 完成，双握手切换
</thinking> → 赋予 phase id="final"
```

### 3. 最终富文本渲染流程
- **phase id="final"** 激活后：
    - 思考框关闭并渐隐
    - 显示 SimpleSummaryText
    - 开始最终富文本渲染
- **`<final>` 标签处理**：
    - 只在后台开始渲染
    - 缓存所有富文本 markdown 和 mermaid 语法
    - 准备好后进行流式渲染
    - `</final>` 标签不做任何时序传递记录

## 具体文件修改要求

### `ThinkingStageCard.kt`
- **标题样式**: 使用金属字体效果，粗体，字号比正文大 1 号
- **正文内容**: 普通渲染，不使用金属动画效果
- **移除**: 状态提示小点组件

### `StreamingFinalRenderer.kt`
- **移除**: "正在接收内容"的预设提示
- **统一渲染器**: 作为打字机效果的统一渲染器

### `ScrollToBottomBtn.kt`
- **布局**: 左右居中对齐

### `FinalActionsRow.kt`
- **复制按钮**: 放置在左边
- **Token 计数**:
    - 格式：`~tokens: 00`
    - 样式：斜体，灰色偏浅

### `ThinkingHeader.kt`
- **职责**: 用户发送消息后立即展现，减少等待时间
- **生命周期**: 等待 perthink 开始后渐隐消失让位

## 渲染完成后的处理
- 使用 `AutoScrollManager.kt` 管理滚动
- 渲染结束后显示 `FinalActionsRow.kt`
- **重要**: phase id="final" 激活后，后续标签不需要任何时序传递，全部内容进入缓存+后台渲染

请确保所有修改都符合双时序架构要求，并参考 `finalmermaid大纲.md` 中的需求规范。
