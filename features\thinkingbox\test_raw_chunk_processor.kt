import com.example.gymbro.features.thinkingbox.domain.processor.RawChunkProcessor

fun main() {
    val processor = RawChunkProcessor()
    
    // 测试用例1：完整的phase标签
    val test1 = "<think><phase:PLAN>我们正在分析问题</phase:PLAN></think>"
    val result1 = processor.preprocess(test1)
    println("测试1:")
    println("输入: $test1")
    println("输出: $result1")
    println("期望: <think>我们正在分析问题</think>")
    println("通过: ${result1 == "<think>我们正在分析问题</think>"}")
    println()
    
    // 测试用例2：不完整的phase标签
    val test2 = "<think><phase:PLAN>分析内容"
    val result2 = processor.preprocess(test2)
    println("测试2:")
    println("输入: $test2")
    println("输出: $result2")
    println("期望: <think>分析内容")
    println("通过: ${result2 == "<think>分析内容"}")
    println()
    
    // 测试用例3：混合内容
    val test3 = "<think><phase:PLAN>分析步骤</phase:PLAN>其他思考内容</think>"
    val result3 = processor.preprocess(test3)
    println("测试3:")
    println("输入: $test3")
    println("输出: $result3")
    println("期望: <think>分析步骤其他思考内容</think>")
    println("通过: ${result3 == "<think>分析步骤其他思考内容</think>"}")
}
