package com.example.gymbro.features.thinkingbox.domain.processor

import org.junit.Assert.*
import org.junit.Test

/**
 * RawChunkProcessor测试 - 验证用户修复：只删除<phase:XXX>标签，保留文本内容
 */
class RawChunkProcessorTest {

    private val processor = RawChunkProcessor()

    @Test
    fun `应该保留phase标签内的文本内容`() {
        // Given: 包含phase标签的文本
        val input = "<phase:PLAN>我们正在分析问题</phase:PLAN>"

        // When: 预处理
        val result = processor.preprocess(input)

        // Then: 应该保留文本内容，删除标签
        assertEquals("我们正在分析问题", result)
    }

    @Test
    fun `应该处理混合内容`() {
        // Given: 混合内容
        val input = "<think><phase:PLAN>分析步骤</phase:PLAN>其他思考内容</think>"

        // When: 预处理
        val result = processor.preprocess(input)

        // Then: 应该保留所有文本内容，只删除phase标签
        assertEquals("<think>分析步骤其他思考内容</think>", result)
    }

    @Test
    fun `应该处理perthink内部的phase标签`() {
        // Given: perthink内部包含phase:PLAN标签的情况
        val input = "<think><phase:PLAN>我们正在分析问题</phase:PLAN></think>"

        // When: 预处理
        val result = processor.preprocess(input)

        // Then: 应该保留文本内容，删除phase标签
        assertEquals("<think>我们正在分析问题</think>", result)
    }

    @Test
    fun `应该处理不完整的phase标签`() {
        // Given: 不完整的phase标签（可能在token边界被截断）
        val input = "<think><phase:PLAN>分析内容"

        // When: 预处理
        val result = processor.preprocess(input)

        // Then: 应该删除开始标签，保留文本内容
        assertEquals("<think>分析内容", result)
    }

    @Test
    fun `应该删除单独的phase开始标签`() {
        // Given: 单独的开始标签
        val input = "<phase:PLAN>一些文本内容"

        // When: 预处理
        val result = processor.preprocess(input)

        // Then: 应该删除标签，保留文本
        assertEquals("一些文本内容", result)
    }

    @Test
    fun `应该删除单独的phase结束标签`() {
        // Given: 单独的结束标签
        val input = "一些文本内容</phase:PLAN>"

        // When: 预处理
        val result = processor.preprocess(input)

        // Then: 应该删除标签，保留文本
        assertEquals("一些文本内容", result)
    }

    @Test
    fun `应该处理多个phase标签`() {
        // Given: 多个phase标签
        val input = "<phase:PLAN>第一部分</phase:PLAN>中间文本<phase:EXECUTE>第二部分</phase:EXECUTE>"

        // When: 预处理
        val result = processor.preprocess(input)

        // Then: 应该保留所有文本内容
        assertEquals("第一部分中间文本第二部分", result)
    }

    @Test
    fun `应该处理嵌套在think标签中的phase标签`() {
        // Given: 嵌套结构
        val input = "<think><phase:PLAN>我们需要分析</phase:PLAN>这个问题的核心</think>"

        // When: 预处理
        val result = processor.preprocess(input)

        // Then: 应该保留think标签和文本内容，删除phase标签
        assertEquals("<think>我们需要分析这个问题的核心</think>", result)
    }

    @Test
    fun `应该处理空的phase标签`() {
        // Given: 空的phase标签
        val input = "<phase:PLAN></phase:PLAN>"

        // When: 预处理
        val result = processor.preprocess(input)

        // Then: 应该删除标签，结果为空
        assertEquals("", result)
    }

    @Test
    fun `应该保持正常XML标签不变`() {
        // Given: 正常的XML标签
        val input = "<thinking><phase id=\"1\">正常内容</phase></thinking>"

        // When: 预处理
        val result = processor.preprocess(input)

        // Then: 应该保持不变
        assertEquals("<thinking><phase id=\"1\">正常内容</phase></thinking>", result)
    }

    @Test
    fun `hasIllegalTags应该正确检测phase标签`() {
        // Given: 包含phase标签的文本
        val input1 = "<phase:PLAN>文本</phase:PLAN>"
        val input2 = "<phase:PLAN>文本"
        val input3 = "文本</phase:PLAN>"
        val input4 = "<thinking>正常内容</thinking>"

        // When & Then
        assertTrue(processor.hasIllegalTags(input1))
        assertTrue(processor.hasIllegalTags(input2))
        assertTrue(processor.hasIllegalTags(input3))
        assertFalse(processor.hasIllegalTags(input4))
    }
}
