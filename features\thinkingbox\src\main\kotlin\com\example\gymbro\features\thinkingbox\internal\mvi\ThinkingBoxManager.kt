package com.example.gymbro.features.thinkingbox.internal.mvi

// 🔥 【V2系统提升】DomainMapperStateless已移除，使用V2系统
import kotlinx.coroutines.CoroutineScope
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ThinkingBoxManager - ThinkingBox 实例管理器
 *
 * 🔥 多轮对话状态隔离解决方案：
 * - 管理多个 ThinkingBox 实例
 * - 按 messageId 隔离状态
 * - 支持实例生命周期管理
 * - 支持并发处理多个对话
 */
@Singleton
class ThinkingBoxManager @Inject constructor(
    @com.example.gymbro.features.thinkingbox.internal.di.ThinkingBoxScope
    private val coroutineScope: CoroutineScope,
    private val streamingParser:
    com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser,
    // 🔥 【V2系统提升】V1映射器已移除，使用V2系统
    private val historySaver: com.example.gymbro.features.thinkingbox.internal.history.HistorySaver,
    // 🔥 【多轮对话架构升级】注入TokenRouter以获取ConversationScope
    private val tokenRouter: com.example.gymbro.core.network.router.TokenRouter,
    // 🔥 【中优先级修复2】注入TokenizerService用于token计算
    private val tokenizerService: com.example.gymbro.core.ai.tokenizer.TokenizerService,
    // 🔥 【perthink修复】注入RawChunkProcessor用于XML标签清洗
    private val rawChunkProcessor: com.example.gymbro.features.thinkingbox.domain.processor.RawChunkProcessor,
) {
    companion object {
        private const val TAG = "ThinkingBoxManager"
        private const val CLEANUP_INTERVAL_MS = 30 * 60 * 1000L // 30分钟
    }

    // 🔥 按 messageId 管理独立实例
    private val instances = ConcurrentHashMap<String, ThinkingBoxInstance>()

    /**
     * 获取或创建 ThinkingBox 实例
     * 🔥 【多轮对话架构升级】从TokenRouter获取ConversationScope并传递给ThinkingBoxInstance
     * 🔥 【实例唯一性验证】确保每个messageId只有一个实例
     */
    fun getOrCreateInstance(
        messageId: String,
        onAiMessageComplete: (
            (
                messageId: String,
                finalMarkdown: String,
                thinkingNodes: String?,
            ) -> Unit
        )? = null,
    ): ThinkingBoxInstance =
        instances.computeIfAbsent(messageId) { id ->
            Timber.tag(TAG).w("🔥 [实例管理] 创建新的ThinkingBox实例: messageId=$id, 当前实例数=${instances.size}")

            // 🔥 【实例泄漏检测】检查是否有过多实例
            if (instances.size > 10) {
                Timber.tag(TAG).e("🚨 [实例泄漏警告] ThinkingBox实例数量过多: ${instances.size}, 可能存在内存泄漏!")
                Timber.tag(TAG).e("🚨 [实例泄漏警告] 现有实例: ${instances.keys.toList()}")
            }

            // 🔥 【多轮对话架构升级】从TokenRouter获取或创建ConversationScope
            val conversationScope = tokenRouter.getOrCreateScope(id)
            Timber.tag(TAG).d("🔥 获取ConversationScope: messageId=$id, scope=${conversationScope.hashCode()}")

            ThinkingBoxInstance(
                messageId = id,
                conversationScope = conversationScope,
                // 🔥 【V2系统提升】V1映射器已移除，ThinkingBoxInstance内部使用V2系统
                historySaver = historySaver,
                tokenizerService = tokenizerService,
                onAiMessageComplete = onAiMessageComplete,
            ).also { instance ->
                Timber.tag(
                    TAG,
                ).w("🎯 [实例验证] ThinkingBox实例创建完成: messageId=$id, instance=${instance.hashCode()}")
            }
        }.also { existingInstance ->
            // 🔥 【实例重用验证】记录实例重用情况
            if (instances.containsKey(messageId)) {
                Timber.tag(
                    TAG,
                ).d(
                    "🔄 [实例重用] 重用现有ThinkingBox实例: messageId=$messageId, instance=${existingInstance.hashCode()}",
                )
            }
        }

    /**
     * 移除指定的实例
     */
    fun removeInstance(messageId: String) {
        instances.remove(messageId)?.let { instance ->
            Timber.tag(TAG).d("🔥 移除ThinkingBox实例: messageId=$messageId")
            instance.cleanup()
            // 🔥 【ThinkingBox流式修复】释放对应的ConversationScope
            try {
                tokenRouter.releaseScope(messageId)
                Timber.tag(TAG).d("✅ 已释放ConversationScope: messageId=$messageId")
            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "⚠️ 释放ConversationScope失败: messageId=$messageId")
            }
        }
    }

    /**
     * 获取当前实例数量
     */
    fun getInstanceCount(): Int = instances.size

    /**
     * 获取所有活跃的 messageId
     */
    fun getActiveMessageIds(): Set<String> = instances.keys.toSet()

    /**
     * 清理已完成且超时的实例
     */
    fun cleanupCompletedInstances() {
        val toRemove = mutableListOf<String>()

        instances.forEach { (messageId, instance) ->
            if (instance.isCompleted() && instance.isOlderThan(CLEANUP_INTERVAL_MS)) {
                toRemove.add(messageId)
            }
        }

        toRemove.forEach { messageId ->
            removeInstance(messageId)
            Timber.tag(TAG).d("🔥 清理超时实例: messageId=$messageId")
        }

        if (toRemove.isNotEmpty()) {
            Timber.tag(TAG).i("🔥 清理完成，移除了 ${toRemove.size} 个实例，当前活跃实例: ${instances.size}")
        }
    }

    /**
     * 强制清理所有实例
     */
    fun clearAllInstances() {
        val count = instances.size
        // 🔥 【ThinkingBox流式修复】逐个移除实例，确保ConversationScope被正确释放
        val messageIds = instances.keys.toList()
        messageIds.forEach { messageId ->
            removeInstance(messageId) // 这个方法已经包含了cleanup和ConversationScope释放
        }
        Timber.tag(TAG).i("🔥 强制清理所有实例，共清理 $count 个实例")
    }

    /**
     * 🔥 【Phase转换修复】获取指定实例
     */
    fun getInstance(messageId: String): ThinkingBoxInstance? = instances[messageId]

    /**
     * 获取实例状态信息（用于调试）
     */
    fun getInstancesInfo(): Map<String, String> {
        return instances.mapValues { (messageId, instance) ->
            "completed=${instance.isCompleted()}, age=${System.currentTimeMillis() - instance.getCreatedAt()}ms"
        }
    }
}
