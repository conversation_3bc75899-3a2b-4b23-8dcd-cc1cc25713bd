package com.example.gymbro.features.thinkingbox.domain.processor

import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 原始chunk预处理器
 *
 * 🎯 P0修复：预清洗非法 `<phase:XXX>` 标签，保证XML合法
 *
 * 问题：AI输出 `<phase:PLAN>我们正在<phase:PLAN>帮助...`
 * 导致：XML解析器把 `<phase:PLAN>` 当成未闭合标签，吞掉其后的文本
 * 解决：在XML解析前预清洗，移除所有非法标签
 */
@Singleton
class RawChunkProcessor @Inject constructor() {

    companion object {
        private const val TAG = "RAW-PROCESSOR"

        // 🔥 【用户修复】只删除<phase:XXX>标签，保留文本内容
        // 匹配 <phase:XXX>文本内容</phase:XXX> 并提取文本内容
        private val PHASE_TAG_WITH_CONTENT =
            Regex("<phase:([A-Z_]+)>(.*?)</phase:\\1>", RegexOption.DOT_MATCHES_ALL)

        // 🔥 匹配单独的开始和结束标签
        private val PHASE_START_TAG = Regex("<phase:[A-Z_]+>")
        private val PHASE_END_TAG = Regex("</phase:[A-Z_]+>")

        // 🔥 其他可能的非法标签模式 (保持现有防御)
        private val OTHER_ILLEGAL_PATTERNS = listOf(
            Regex("<[a-zA-Z]+:[a-zA-Z_]+>"), // 匹配 <any:any> 格式，但不包括合法的XML命名空间
            // 🔥 【关键修复】移除<phase xxx>格式的过滤，因为<phase id="1">是合法的XML标签
            // Regex("<phase\\s+[^>]*>"),           // 这个会错误删除<phase id="1">标签！
        )
    }

    /**
     * 预处理原始chunk，清洗非法标签
     *
     * 🔥 【用户修复】只删除<phase:XXX>标签，保留文本内容
     * 例如：<phase:PLAN>我们正在分析</phase:PLAN> → 我们正在分析
     *
     * @param rawChunk AI输出的原始chunk
     * @return 清洗后的合法XML chunk
     */
    fun preprocess(rawChunk: String): String {
        if (rawChunk.isEmpty()) return rawChunk

        var currentChunk = rawChunk

        // 🔥 【用户修复】第一步：处理完整的<phase:XXX>文本</phase:XXX>，保留文本内容
        PHASE_TAG_WITH_CONTENT.findAll(currentChunk).forEach { match ->
            val fullMatch = match.value
            val textContent = match.groupValues[2] // 提取文本内容
            Timber.tag(TAG).i("🔥 [用户修复] 提取phase标签内容: '$fullMatch' → '$textContent'")
            currentChunk = currentChunk.replace(fullMatch, textContent)
        }

        // 🔥 【perthink修复】增强清洗：处理分块token中的不完整标签
        if (currentChunk.contains("<think>") ||
            (currentChunk.contains("<phase:") && !currentChunk.contains("<thinking>") && !currentChunk.contains("<phase id="))
        ) {
            // PRE_THINK 模式：删除单独的开始和结束标签
            if (PHASE_START_TAG.containsMatchIn(currentChunk)) {
                Timber.tag(TAG).w("🔥 [PRE_THINK] 删除phase开始标签")
                currentChunk = currentChunk.replace(PHASE_START_TAG, "")
            }

            if (PHASE_END_TAG.containsMatchIn(currentChunk)) {
                Timber.tag(TAG).w("🔥 [PRE_THINK] 删除phase结束标签")
                currentChunk = currentChunk.replace(PHASE_END_TAG, "")
            }

            // 🔥 【新增】处理可能的不完整标签（在token边界被截断）
            // 删除不完整的开始标签，如 "<phase:PL" 或 "<phase:PLAN"
            currentChunk = currentChunk.replace(Regex("<phase:[A-Z_]*$"), "")
            // 删除不完整的结束标签，如 "</phase:PL" 或 "AN>"
            currentChunk = currentChunk.replace(Regex("^[A-Z_]*>"), "")
            currentChunk = currentChunk.replace(Regex("</phase:[A-Z_]*$"), "")
        } else {
            // 🔥 【711文本截断修复】THINKING模式或其他模式：保持原样
            Timber.tag(TAG).d("🔥 [文本截断修复] 保持原样：不删除phase标签")
        }

        // 🔥 第三步：处理其他非法标签模式
        OTHER_ILLEGAL_PATTERNS.forEach { pattern ->
            if (pattern.containsMatchIn(currentChunk)) {
                Timber.tag(TAG).w("清洗其他非法标签, pattern: ${pattern.pattern}")
                currentChunk = currentChunk.replace(pattern, "")
            }
        }

        // 🔥 验证脚本：记录清洗效果
        if (rawChunk.length != currentChunk.length) {
            Timber.tag(TAG).i("🔥 [用户修复] 清洗完成: 原始=${rawChunk.length}字符, 清洗后=${currentChunk.length}字符")
            if (rawChunk.length != currentChunk.length) {
                Timber.tag(TAG).i("🔥 [用户修复] 处理了${rawChunk.length - currentChunk.length}个字符的标签内容")
            }
        }

        return currentChunk
    }

    /**
     * 检查chunk是否包含非法标签
     */
    fun hasIllegalTags(chunk: String): Boolean {
        return PHASE_TAG_WITH_CONTENT.containsMatchIn(chunk) ||
            PHASE_START_TAG.containsMatchIn(chunk) ||
            PHASE_END_TAG.containsMatchIn(chunk) ||
            OTHER_ILLEGAL_PATTERNS.any { it.containsMatchIn(chunk) }
    }

    /**
     * 获取清洗统计信息
     */
    fun getCleaningStats(originalChunk: String, cleanedChunk: String): CleaningStats {
        val removedChars = originalChunk.length - cleanedChunk.length
        val illegalTagCount = PHASE_TAG_WITH_CONTENT.findAll(originalChunk).count() +
            PHASE_START_TAG.findAll(originalChunk).count() +
            PHASE_END_TAG.findAll(originalChunk).count() +
            OTHER_ILLEGAL_PATTERNS.sumOf { pattern ->
                pattern.findAll(originalChunk).count()
            }

        return CleaningStats(
            originalLength = originalChunk.length,
            cleanedLength = cleanedChunk.length,
            removedChars = removedChars,
            illegalTagCount = illegalTagCount,
        )
    }

    /**
     * 🔥 【711全链路修复】检测是否处于 PRE_THINK 模式
     */
    private fun detectPreThinkMode(chunk: String): Boolean {
        // 简单的启发式检测：如果chunk包含<think>或者在<think>和</think>之间
        return chunk.contains("<think>") ||
            (chunk.contains("<phase:") && !chunk.contains("<thinking>"))
    }

    /**
     * 🔥 【711全链路修复】检测是否处于 THINKING 模式
     */
    private fun detectThinkingMode(chunk: String): Boolean {
        // 简单的启发式检测：如果chunk包含<thinking>
        return chunk.contains("<thinking>")
    }
}

/**
 * 清洗统计信息
 */
data class CleaningStats(
    val originalLength: Int,
    val cleanedLength: Int,
    val removedChars: Int,
    val illegalTagCount: Int,
) {
    val cleaningEfficiency: Float = if (originalLength > 0) {
        removedChars.toFloat() / originalLength.toFloat()
    } else {
        0f
    }
}
