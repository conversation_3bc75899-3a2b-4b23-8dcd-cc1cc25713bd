package com.example.gymbro.features.thinkingbox.domain.parser

import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.constants.ThinkingBoxStrings
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Test

/**
 * ThinkingBox 集成测试
 *
 * 🎯 测试目标：
 * - 验证 <think>内容</think> → phase id="perthink" 的映射
 * - 确认 6 个统一事件系统的完整流程
 * - 测试双时序架构的事件流
 */
class ThinkingBoxIntegrationTest {

    private val xmlScanner = XmlStreamScanner()
    private val functionCallDetector = FunctionCallDetector()
    private val parser = StreamingThinkingMLParser(xmlScanner, functionCallDetector)
    private val domainMapper = DomainMapper()

    @Test
    fun `should correctly parse think tag to perthink phase`() = runTest {
        // Given: <think> 标签内容
        val input = "<think>我正在思考这个问题...</think>"
        val events = mutableListOf<SemanticEvent>()

        // When: 解析输入
        parser.parseTokenStream(
            messageId = "test-msg",
            tokens = flowOf(input),
            onEvent = { events.add(it) },
        )

        // Then: 验证生成的事件
        assertTrue(
            "应该生成 PhaseStart 事件",
            events.any { it is SemanticEvent.PhaseStart && it.id == "perthink" && it.title == ThinkingBoxStrings.PERTHINK_TITLE },
        )

        assertTrue(
            "应该生成 PhaseContent 事件",
            events.any { it is SemanticEvent.PhaseContent && it.id == "perthink" },
        )

        assertTrue(
            "应该生成 PreThinkChunk 事件",
            events.any { it is SemanticEvent.PreThinkChunk },
        )

        assertTrue(
            "应该生成 PreThinkEnd 事件",
            events.any { it is SemanticEvent.PreThinkEnd },
        )

        assertTrue(
            "应该生成 PhaseEnd 事件",
            events.any { it is SemanticEvent.PhaseEnd && it.id == "perthink" },
        )
    }

    @Test
    fun `should correctly map semantic events to thinking events`() {
        // Given: SemanticEvent 列表
        val semanticEvents = listOf(
            SemanticEvent.PhaseStart("perthink", ThinkingBoxStrings.PERTHINK_TITLE),
            SemanticEvent.PreThinkChunk("思考内容"),
            SemanticEvent.PhaseContent("perthink", "思考内容"),
            SemanticEvent.PreThinkEnd,
            SemanticEvent.PhaseEnd("perthink"),
            SemanticEvent.PhaseStart("1", "分析阶段"),
            SemanticEvent.PhaseContent("1", "分析内容"),
            SemanticEvent.PhaseEnd("1"),
            SemanticEvent.FinalArrived("最终答案"),
        )

        // When: 映射事件
        val thinkingEvents = mutableListOf<ThinkingEvent>()
        var context = DomainMapper.MappingContext()

        semanticEvents.forEach { semanticEvent ->
            val result = domainMapper.mapSemanticToThinking(semanticEvent, context)
            thinkingEvents.addAll(result.events)
            context = result.context
        }

        // Then: 验证映射结果
        assertTrue(
            "应该映射 PhaseStart",
            thinkingEvents.any { it is ThinkingEvent.PhaseStart && it.id == "perthink" },
        )

        assertTrue(
            "应该映射 PreThinkChunk",
            thinkingEvents.any { it is ThinkingEvent.PreThinkChunk },
        )

        assertTrue(
            "应该映射 PhaseContent",
            thinkingEvents.any { it is ThinkingEvent.PhaseContent && it.id == "perthink" },
        )

        assertTrue(
            "应该映射 PreThinkEnd",
            thinkingEvents.any { it is ThinkingEvent.PreThinkEnd },
        )

        assertTrue(
            "应该映射 PhaseEnd",
            thinkingEvents.any { it is ThinkingEvent.PhaseEnd && it.id == "perthink" },
        )

        assertTrue(
            "应该映射正式阶段",
            thinkingEvents.any { it is ThinkingEvent.PhaseStart && it.id == "1" },
        )

        assertTrue(
            "应该映射 FinalArrived",
            thinkingEvents.any { it is ThinkingEvent.FinalArrived },
        )
    }

    @Test
    fun `should handle complex thinking flow`() = runTest {
        // Given: 复杂的思考流程
        val input = """
            <think>预思考内容</think>
            <thinking>
            <phase id="1"><title>分析阶段</title>
            正在分析问题...
            </phase>
            <phase id="2"><title>解决方案</title>
            提出解决方案...
            </phase>
            </thinking>
            <final>最终答案内容</final>
        """.trimIndent()

        val events = mutableListOf<SemanticEvent>()

        // When: 解析复杂流程
        parser.parseTokenStream(
            messageId = "complex-test",
            tokens = flowOf(input),
            onEvent = { events.add(it) },
        )

        // Then: 验证完整流程
        val phaseStartEvents = events.filterIsInstance<SemanticEvent.PhaseStart>()
        assertEquals("应该有3个阶段开始事件（perthink + 2个正式阶段）", 3, phaseStartEvents.size)

        val phaseEndEvents = events.filterIsInstance<SemanticEvent.PhaseEnd>()
        assertEquals("应该有3个阶段结束事件", 3, phaseEndEvents.size)

        assertTrue("应该有PreThinkEnd事件", events.any { it is SemanticEvent.PreThinkEnd })
        assertTrue("应该有FinalArrived事件", events.any { it is SemanticEvent.FinalArrived })
    }
}
