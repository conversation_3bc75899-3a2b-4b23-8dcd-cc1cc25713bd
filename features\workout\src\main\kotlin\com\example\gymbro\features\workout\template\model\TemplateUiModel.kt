package com.example.gymbro.features.workout.template.model

import androidx.compose.runtime.Immutable
import com.example.gymbro.shared.models.workout.WorkoutTemplateDto
import com.example.gymbro.shared.models.workout.summary

/**
 * 模板UI模型
 *
 * 用于Template模块UI层的数据展示，符合MVI 2.0架构标准
 * 提供统一的数据接口，简化UI组件的数据绑定
 */
@Immutable
data class TemplateUiModel(
    /**
     * 模板唯一标识符
     */
    val id: String,

    /**
     * 模板名称
     */
    val name: String,

    /**
     * 模板摘要信息
     */
    val summary: String,

    /**
     * 模板描述
     */
    val description: String?,

    /**
     * 是否为草稿状态
     */
    val isDraft: Boolean,

    /**
     * 是否已发布
     */
    val isPublished: Boolean,

    /**
     * 预计训练时长（分钟）
     */
    val estimatedDuration: Int?,

    /**
     * 目标肌群列表
     */
    val targetMuscleGroups: List<String>,

    /**
     * 难度等级
     */
    val difficultyLevel: String?,

    /**
     * 标签列表
     */
    val tags: List<String>,

    /**
     * 是否收藏
     */
    val isFavorite: Boolean,

    /**
     * 使用次数
     */
    val usageCount: Int,

    /**
     * 创建时间戳
     */
    val createdAt: Long,

    /**
     * 更新时间戳
     */
    val updatedAt: Long
) {
    companion object {
        /**
         * 从WorkoutTemplateDto创建TemplateUiModel
         */
        fun fromWorkoutTemplateDto(dto: WorkoutTemplateDto): TemplateUiModel {
            return TemplateUiModel(
                id = dto.id,
                name = dto.name,
                summary = dto.summary,
                description = dto.description,
                isDraft = dto.actualIsDraft,
                isPublished = dto.actualIsPublished,
                estimatedDuration = dto.metadata?.estimatedDuration,
                targetMuscleGroups = dto.metadata?.targetMuscleGroups ?: emptyList(),
                difficultyLevel = dto.difficultyLevel?.toString(),
                tags = dto.metadata?.tags ?: emptyList(),
                isFavorite = dto.metadata?.isPublic ?: false, // 注意：收藏状态独立管理，此处使用默认值
                usageCount = dto.metadata?.popularity ?: 0,
                createdAt = dto.createdAt,
                updatedAt = dto.updatedAt
            )
        }

        /**
         * 创建预览用的示例数据
         */
        fun createPreviewData(): TemplateUiModel {
            return TemplateUiModel(
                id = "template_preview_001",
                name = "胸部力量训练",
                summary = "总计 1250.0 kg • 4 组",
                description = "专注于胸部肌群的力量训练模板",
                isDraft = false,
                isPublished = true,
                estimatedDuration = 45,
                targetMuscleGroups = listOf("胸部", "三头肌"),
                difficultyLevel = "中等",
                tags = listOf("力量", "胸部", "上肢"),
                isFavorite = true,
                usageCount = 15,
                createdAt = System.currentTimeMillis() - 86400000, // 1天前
                updatedAt = System.currentTimeMillis()
            )
        }
    }
}

/**
 * TemplateUiModel扩展属性
 * 提供便捷的UI展示属性
 */

/**
 * 获取格式化的难度等级显示文本
 */
val TemplateUiModel.difficultyDisplayText: String
    get() = difficultyLevel ?: "未设置"

/**
 * 获取格式化的时长显示文本
 */
val TemplateUiModel.durationDisplayText: String
    get() = estimatedDuration?.let { "${it}分钟" } ?: "未设置"

/**
 * 获取状态显示文本
 */
val TemplateUiModel.statusDisplayText: String
    get() = when {
        isDraft -> "草稿"
        isPublished -> "已发布"
        else -> "未发布"
    }

/**
 * 获取目标肌群的显示文本
 */
val TemplateUiModel.muscleGroupsDisplayText: String
    get() = if (targetMuscleGroups.isNotEmpty()) {
        targetMuscleGroups.joinToString(", ")
    } else {
        "全身"
    }

/**
 * 获取标签的显示文本
 */
val TemplateUiModel.tagsDisplayText: String
    get() = if (tags.isNotEmpty()) {
        tags.joinToString(" • ")
    } else {
        ""
    }
