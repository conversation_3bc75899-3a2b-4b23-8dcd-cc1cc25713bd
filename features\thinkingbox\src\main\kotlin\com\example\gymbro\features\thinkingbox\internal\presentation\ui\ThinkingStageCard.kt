package com.example.gymbro.features.thinkingbox.internal.presentation.ui

import androidx.compose.animation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.components.extras.rememberMetallicBrush
import com.example.gymbro.designSystem.components.text.ProgressiveTextRenderer
import com.example.gymbro.designSystem.components.text.RenderSpeed
import com.example.gymbro.designSystem.theme.coachTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi
import com.example.gymbro.features.thinkingbox.internal.constants.ThinkingBoxStrings
import com.example.gymbro.features.thinkingbox.internal.presentation.ui.AnimationEngine
import kotlinx.coroutines.delay
import timber.log.Timber

/**
 * ThinkingStageCard - ChatGPT风格简洁卡片设计
 *
 * 统一的思考阶段卡片，支持perthink和正式phase
 * 布局：title → 文本内容（无多余分割线和空间）
 * 🔥 【AnimatedContent动画】当检测到phase结束时，实现平滑的淡入淡出过渡效果
 * 🔥 【保留ChatGPT风格】简洁的圆角卡片设计，但恢复原始的动画系统
 */
@Composable
fun ThinkingStageCard(
    phase: PhaseUi,
    isPreThink: Boolean = false,
    isActive: Boolean = true,
    modifier: Modifier = Modifier,
    // 🔥 【问题3修复】添加动画完成回调，用于触发phase切换
    onAnimationFinished: ((String) -> Unit)? = null,
) {
    // 1/3屏高度限制
    val maxHeight = (LocalConfiguration.current.screenHeightDp * 0.33f).dp

    Card(
        modifier =
        modifier
            .fillMaxWidth()
            .heightIn(max = maxHeight)
            .let {
                // 🔥 【动—静双层方案】卡片层：elevation + scale 动画
                AnimationEngine.run { it.phaseTransitionAnimation(isActive) }
            },
        shape = RoundedCornerShape(Tokens.Radius.Medium),
        elevation = CardDefaults.cardElevation(Tokens.Elevation.Small),
        colors =
        CardDefaults.cardColors(
            containerColor = MaterialTheme.coachTheme.backgroundPrimary,
        ),
    ) {
        // 🔥 【修复phase卡住问题】移除AnimatedContent，避免双重动画导致phase切换过早触发
        // phase切换动画由AIThinkingCard统一管理，ThinkingStageCard只负责内容展示
        Column(
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        ) {
        // 🔥 【恢复perthink标题】为perthink阶段显示固定标题，使用字符串常量
        val displayTitle = when {
            isPreThink && phase.id == "perthink" -> ThinkingBoxStrings.PERTHINK_TITLE // perthink固定标题
            !phase.title.isNullOrBlank() -> phase.title // 真实title
            else -> null // 无title时不显示header
        }

        // 🔥 【Title显示优化】只有在有title时才显示header，避免空白区域
        displayTitle?.let { title ->
            ChatGptThinkingHeader(
                title = title,
                phaseId = phase.id,
                isActive = isActive,
                isPreThink = isPreThink,
            )

            // 🔥 【1行间距】Header和内容间使用Small(8dp)间距，相当于1行间距
            Spacer(modifier = Modifier.height(Tokens.Spacing.Small))
        }

        // 🔥 【内容显示】打字机文本内容 - 直接跟在title后面，无额外间距
        if (phase.content.isNotEmpty()) {

                            // 🔥 【统一渲染器修复】使用ProgressiveTextRenderer，符合当前架构
            ProgressiveTextRenderer(
                fullText = phase.content,
                modifier = Modifier.fillMaxWidth(),
                renderSpeed = if (isPreThink) RenderSpeed.FAST else RenderSpeed.COMFORTABLE, // perthink更快，正式phase标准速度
                onComplete = {
                    // 🔥 【UI改动回调修复】渲染完成后触发动画完成回调
                    Timber.tag("TB-ANIM").d("🎬 [Phase切换] ${if (isPreThink) "PreThink" else "FormalPhase"} ${phase.id} 渲染完成，触发回调")
                    onAnimationFinished?.invoke(phase.id)
                }
            )
            }
        }
    }

    // 🔥 【UI改动回调修复】移除LaunchedEffect中的重复回调，只由打字机onDone触发
    // 双握手机制现在完全由UI动画完成（打字机onDone）触发，符合finalmermaid大纲要求
}

/**
 * ChatGPT风格思考Header组件 - 724方案修复：移除状态提示小点
 */
@Composable
private fun ChatGptThinkingHeader(
    title: String,
    phaseId: String,
    isActive: Boolean,
    isPreThink: Boolean = false,
) {
    // 🔥 【724方案修复】移除StatusDot，只保留MetallicText标题
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // 🔥 【724方案修复】标题使用金属字体效果，粗体，字号比正文大1号
        ChatGptMetallicText(
            text = title,
            phaseId = phaseId,
            isPreThink = isPreThink,
            modifier = Modifier.weight(1f),
        )
    }
}

/**
 * MetallicStreamingText - 流式金属质感文本渲染器
 *
 * 🎯 **核心架构**: 替换原有的typewriter动画系统，使用类似StreamingFinalRenderer的MetallicStreamingText效果
 * - 流式显示: 字符逐个显示，delay(100)实现打字机视觉效果
 * - 双时序支持: 支持dual timing sequence机制，确保动画回调接口正确匹配
 * - 金属质感: 保持原有的metallic brush效果
 * - 性能优化: 避免重复渲染和不必要的状态重置
 */
@Composable
private fun MetallicStreamingText(
    fullText: String,
    phaseId: String,
    isPreThink: Boolean = false,
    modifier: Modifier = Modifier,
    onDone: (() -> Unit)? = null,
) {
    // 🔥 【流式状态管理】跟踪已显示的字符数量
    var displayedCharsCount by remember(phaseId, fullText) { mutableStateOf(0) }
    var isStreamingActive by remember(phaseId, fullText) { mutableStateOf(false) }

    // 🔥 【MetallicStreaming核心】响应文本变化，实现流式字符显示
    LaunchedEffect(fullText.length, phaseId) {
        if (fullText.length > displayedCharsCount && !isStreamingActive) {
            isStreamingActive = true

            // 🔥 【双时序架构】根据phase类型调整streaming速度
            val charDelay = if (isPreThink) 50L else 100L // perthink更快，正式phase使用标准100ms

            // 🔥 【流式显示】从当前位置继续显示新字符
            for (i in displayedCharsCount until fullText.length) {
                displayedCharsCount = i + 1
                delay(charDelay) // 🔥 核心：使用delay(100)实现MetallicStreamingText效果
            }

            isStreamingActive = false

            // 🔥 【动画完成回调】支持双时序列机制
            if (displayedCharsCount >= fullText.length) {
                onDone?.invoke()
            }
        }
    }

    // 🔥 【金属质感】保持原有的metallic brush效果
    val metallicBrush = rememberMetallicBrush(
        useAnimate = true,
        rotationDurationMillis = if (isPreThink) 2000 else 3000,
        useHdr = false,
    )

    // 🔥 【内容显示】显示已流式显示的字符
    val displayedContent = fullText.take(displayedCharsCount)

    Text(
        text = displayedContent,
        style = MaterialTheme.typography.bodyMedium.copy(
            brush = metallicBrush,
        ),
        color = MaterialTheme.coachTheme.textPrimary,
        modifier = modifier,
    )
}



/**
 * 金属质感标题文本 - 支持打字机效果和流式金属动画
 */
@Composable
private fun ChatGptMetallicText(
    text: String,
    phaseId: String,
    isPreThink: Boolean = false,
    modifier: Modifier = Modifier,
) {
    // 🔥 【流式状态管理】跟踪已显示的字符数量
    var displayedCharsCount by remember(phaseId, text) { mutableStateOf(0) }
    var isStreamingActive by remember(phaseId, text) { mutableStateOf(false) }

    // 🔥 【Title打字机效果】响应文本变化，实现流式字符显示
    LaunchedEffect(text.length, phaseId) {
        if (text.length > displayedCharsCount && !isStreamingActive) {
            isStreamingActive = true

            // 🔥 【双时序架构】根据phase类型调整streaming速度
            val charDelay = if (isPreThink) 25L else 45L // perthink更快，正式phase使用标准速度

            // 🔥 【流式显示】从当前位置继续显示新字符
            for (i in displayedCharsCount until text.length) {
                displayedCharsCount = i + 1
                delay(charDelay)
            }

            isStreamingActive = false
        }
    }

    // 🔥 【内容显示】显示已流式显示的字符
    val displayedContent = text.take(displayedCharsCount)

    // 🔥 【修复：使用RainbowAnimations的rememberStreamingMetallicBrush】
    val streamingMetallicBrush = com.example.gymbro.designSystem.components.extras.rememberStreamingMetallicBrush(
        useAnimate = true,
        useHdr = false,
    )

    Text(
        text = displayedContent,
        style = MaterialTheme.typography.titleMedium.copy(
            fontStyle = if (isPreThink) FontStyle.Italic else FontStyle.Normal, // perthink斜体
            fontWeight = FontWeight.Bold,
            brush = streamingMetallicBrush, // 🔥 【修复】使用流式金属动画
        ),
        modifier = modifier.let {
            if (isPreThink) {
                // 🔥 【perthink特效】添加脉冲动画
                AnimationEngine.run { it.pulseAnimation(enabled = true) }
            } else {
                it
            }
        },
        maxLines = 1,
    )
}

// ==================== Preview Functions ====================

@Composable
private fun createMockPhaseUi(
    id: String,
    title: String,
    content: String,
    isComplete: Boolean = true,
): com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi {
    return com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi(
        id = id,
        title = title,
        content = content,
        isComplete = isComplete,
    )
}

/**
 * ThinkingStageCard Preview - 正式思考阶段
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun ThinkingStageCardPreview_Normal() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        ThinkingStageCard(
            phase = createMockPhaseUi(
                id = "analyze",
                title = "分析问题",
                content = "我需要仔细分析你提出的健身计划问题，考虑你的目标、经验水平和可用时间。根据你的描述，我将从以下几个方面来制定合适的训练方案。",
            ),
            isPreThink = false,
            isActive = true,
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}

/**
 * ThinkingStageCard Preview - PreThink 阶段
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun ThinkingStageCardPreview_PreThink() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        ThinkingStageCard(
            phase = createMockPhaseUi(
                id = "perthink",
                title = ThinkingBoxStrings.PERTHINK_TITLE,
                content = "让我想想...",
            ),
            isPreThink = true,
            isActive = true,
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}

/**
 * ThinkingStageCard Preview - 非活动状态
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun ThinkingStageCardPreview_Inactive() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        ThinkingStageCard(
            phase = createMockPhaseUi(
                id = "completed",
                title = "制定方案",
                content = "基于分析结果，我为你制定了个性化的训练计划，包括力量训练和有氧运动的合理搭配。",
            ),
            isPreThink = false,
            isActive = false,
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}

/**
 * ThinkingStageCard Preview - 加载中状态
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun ThinkingStageCardPreview_Loading() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        ThinkingStageCard(
            phase = createMockPhaseUi(
                id = "planning",
                title = "制定训练计划",
                content = "",
                isComplete = false,
            ),
            isPreThink = false,
            isActive = true,
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}


