package com.example.gymbro.features.workout.template.edit.reducer

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.features.workout.template.TemplateContract
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import com.example.gymbro.features.workout.template.edit.data.TemplateDataMapper
import timber.log.Timber
import javax.inject.Inject

/**
 * Template Edit Intent 处理器
 * 
 * 🎯 职责：
 * - 处理模板编辑相关的 Intent
 * - 纯函数式状态转换
 * - 遵循 MVI Golden Standard
 * 
 * 📋 遵循标准：
 * - 单一职责原则
 * - 纯函数式编程
 * - 不可变状态管理
 */
class TemplateEditIntentHandlers @Inject constructor() {

    // === 模板生命周期管理 ===

    fun handleLoadTemplate(
        intent: TemplateEditContract.Intent.LoadTemplate,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.withEffect(
            state.copy(
                isLoading = true,
                error = null,
            ),
            TemplateEditContract.Effect.LoadTemplateData(intent.templateId),
        )
    }

    // === 保存相关 Intent 处理 ===

    fun handleSaveAsDraft(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        Timber.d("📄 SaveAsDraft转为Effect")
        return ReduceResult.withEffect(
            newState = state.copy(isSaving = true),
            effect = TemplateEditContract.Effect.SaveAsDraft
        )
    }

    fun handleCreateAndSaveImmediately(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        Timber.d("⚡ CreateAndSaveImmediately转为Effect")
        return ReduceResult.withEffect(
            newState = state.copy(isSaving = true),
            effect = TemplateEditContract.Effect.CreateAndSaveImmediately
        )
    }

    fun handlePublishTemplate(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        Timber.d("🚀 PublishTemplate转为Effect")
        return ReduceResult.withEffect(
            newState = state.copy(isSaving = true, isCreatingVersion = true),
            effect = TemplateEditContract.Effect.PublishTemplate
        )
    }

    // === 模板基本信息编辑 ===

    fun handleUpdateTemplateName(
        intent: TemplateEditContract.Intent.UpdateTemplateName,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        println("🔧 [DEBUG] handleUpdateTemplateName: 旧名称='${state.templateName}', 新名称='${intent.name}'")

        val newState = state.copy(
            templateName = intent.name,
            hasUnsavedChanges = true,
            autoSaveState = TemplateContract.AutoSaveState.Inactive,
        )

        println("🔧 [DEBUG] handleUpdateTemplateName: 更新后状态 templateName='${newState.templateName}'")
        return ReduceResult.stateOnly(newState)
    }

    fun handleUpdateTemplateDescription(
        intent: TemplateEditContract.Intent.UpdateTemplateDescription,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(
                templateDescription = intent.description,
                hasUnsavedChanges = true,
                autoSaveState = TemplateContract.AutoSaveState.Inactive,
            ),
        )
    }

    // === 模板数据设置 ===

    fun handleSetTemplate(
        intent: TemplateEditContract.Intent.SetTemplate,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        // 使用统一数据映射器进行 Domain → DTO → UI 转换
        val templateDto = TemplateDataMapper.mapDomainToDto(intent.template)
        val updatedState = TemplateDataMapper.mapDtoToState(
            dto = templateDto,
            currentState = state
        )

        return ReduceResult.withEffect(
            newState = updatedState,
            effect = TemplateEditContract.Effect.TriggerAutoSave,
        )
    }

    fun handleSetCurrentUserId(
        intent: TemplateEditContract.Intent.SetCurrentUserId,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> =
        ReduceResult.stateOnly(
            state.copy(currentUserId = intent.userId),
        )

    fun handleSetVersionHistory(
        intent: TemplateEditContract.Intent.SetVersionHistory,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(versionHistory = intent.versions),
        )
    }

    // === 创建空模板 ===

    fun handleCreateEmptyTemplate(
        intent: TemplateEditContract.Intent.CreateEmptyTemplate,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        println("🔧 [DEBUG] handleCreateEmptyTemplate: 开始创建空模板")

        val templateId = intent.templateId
        val userId = if (state.currentUserId.isNotEmpty()) {
            state.currentUserId
        } else {
            // 🔥 修复：用户ID为空表示还未初始化，返回错误状态
            Timber.e("❌ 创建模板时用户ID为空，用户可能未认证")
            return ReduceResult.withEffect(
                state.copy(
                    error = UiText.DynamicString("用户未认证，无法创建模板"),
                    isLoading = false
                ),
                TemplateEditContract.Effect.ShowError(UiText.DynamicString("用户认证异常"))
            )
        }

        // 创建空的模板对象
        val emptyTemplate = com.example.gymbro.domain.workout.model.template.WorkoutTemplate(
            id = templateId,
            name = "",
            description = "",
            targetMuscleGroups = emptyList(),
            difficulty = 1,
            estimatedDuration = 30,
            userId = userId,
            isPublic = false,
            isFavorite = false,
            tags = emptyList(),
            exercises = emptyList(),
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
            currentVersion = 1,
            isDraft = true,
            isPublished = false,
            lastPublishedAt = null,
        )

        return ReduceResult.withEffect(
            newState = state.copy(
                template = emptyTemplate,
                templateName = "",
                templateDescription = "",
                exercises = emptyList(),
                isDraft = true,
                isPublished = false,
                currentVersion = 1,
                hasUnsavedChanges = false,
                isLoading = false,
                error = null,
            ),
            effect = TemplateEditContract.Effect.CreateAndSaveImmediately
        )
    }
}
