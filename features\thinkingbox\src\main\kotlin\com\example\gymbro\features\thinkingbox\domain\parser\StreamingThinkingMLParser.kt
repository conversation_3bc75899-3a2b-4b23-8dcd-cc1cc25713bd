package com.example.gymbro.features.thinkingbox.domain.parser

import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【方案1：纯解析器模式】StreamingThinkingMLParser v2.0
 *
 * 核心职责：
 * - XML Token 流解析：将原始 token 转换为结构化的 XML 事件
 * - 状态机管理：维护解析器状态（PRE_THINK → THINKING → POST_FINAL）
 * - 基础事件生成：只生成 TagOpened、TagClosed、TextChunk、StreamFinished 事件
 *
 * 边界约束：
 * - 不处理业务逻辑：Phase 生命周期、状态转换等由 DomainMapper 处理
 * - 不维护业务状态：不跟踪 currentPhaseId、phase 内容等
 * - 不做语义解析：只做语法解析，语义映射由 DomainMapper 负责
 */
@Singleton
class StreamingThinkingMLParser @Inject constructor(
    private val xmlScanner: XmlStreamScanner,
) {

    /**
     * 解析器状态枚举
     * 用于状态机管理，确保正确的解析流程
     */
    enum class ParserState {
        PRE_THINK, // <think> 预思考阶段
        THINKING, // <thinking> 正式思考阶段
        POST_FINAL, // </thinking> 后，等待 <final> 阶段
    }

    /**
     * 解析器上下文
     * 维护解析过程中的最小必要状态
     */
    data class ParserContext(
        var state: ParserState = ParserState.PRE_THINK,
    )

    /**
     * 解析 token 流并生成语义事件
     *
     * @param tokenChunk 输入的 token 字符串
     * @param messageId 消息ID，用于日志追踪
     * @param onEvent 事件回调函数
     */
    fun parseTokenChunk(
        tokenChunk: String,
        messageId: String,
        onEvent: (SemanticEvent) -> Unit,
    ) {
        val context = ParserContext()

        try {
            // 扫描 token 流
            val tokens = xmlScanner.feed(tokenChunk)

            // 处理每个 token
            tokens.forEach { token ->
                val events = processToken(token, context, messageId)
                events.forEach { onEvent(it) }
            }
        } catch (e: Exception) {
            Timber.e(e, "[$messageId] XML parsing error")
            onEvent(
                SemanticEvent.ParseErrorEvent(
                    com.example.gymbro.features.thinkingbox.domain.model.ParseError(
                        type = com.example.gymbro.features.thinkingbox.domain.model.ErrorType.PARSING_ERROR,
                        message = e.message ?: "Unknown parsing error",
                    ),
                ),
            )
        }
    }

    /**
     * 🔥 【接口兼容性】原始 parseTokenStream 方法 - 保持向后兼容
     *
     * 这是原始的接口方法，UI层和token流集成代码期望调用此方法。
     * 内部使用新的纯解析器架构实现。
     */
    suspend fun parseTokenStream(
        messageId: String,
        tokens: kotlinx.coroutines.flow.Flow<String>,
        onEvent: suspend (SemanticEvent) -> Unit,
    ) {
        val context = ParserContext()

        try {
            tokens.collect { tokenChunk ->
                // 🔥 【核心解析】记录关键token
                if (tokenChunk.contains("<think>") || tokenChunk.contains("</think>") || tokenChunk.isNotEmpty()) {
                    Timber.tag("TB-PARSER").e("🔥 [核心解析] [$messageId] 解析器收到token: '${tokenChunk.take(50)}...'")
                }

                // 使用新的解析逻辑
                val xmlTokens = xmlScanner.feed(tokenChunk)

                xmlTokens.forEach { token ->
                    val events = processToken(token, context, messageId)
                    events.forEach { event ->
                        onEvent(event)
                    }
                }
            }

            // 流结束时发送完成事件
            onEvent(SemanticEvent.StreamFinished())
        } catch (e: Exception) {
            Timber.e(e, "[$messageId] parseTokenStream error")
            onEvent(
                SemanticEvent.ParseErrorEvent(
                    com.example.gymbro.features.thinkingbox.domain.model.ParseError(
                        type = com.example.gymbro.features.thinkingbox.domain.model.ErrorType.PARSING_ERROR,
                        message = e.message ?: "Unknown parsing error",
                    ),
                ),
            )
        }
    }

    /**
     * 完成解析并生成流结束事件
     */
    fun finishParsing(
        messageId: String,
        onEvent: (SemanticEvent) -> Unit,
    ) {
        Timber.d("[$messageId] 流结束")
        onEvent(SemanticEvent.StreamFinished())
    }

    /**
     * 处理单个 token
     * 根据当前状态路由到对应的处理器
     */
    private fun processToken(
        token: XmlStreamScanner.Token,
        context: ParserContext,
        messageId: String,
    ): List<SemanticEvent> {
        return when (context.state) {
            ParserState.PRE_THINK -> handlePreThink(token, context, messageId)
            ParserState.THINKING -> handleThinking(token, context, messageId)
            ParserState.POST_FINAL -> handlePostFinal(token, context, messageId)
        }
    }

    /**
     * 处理 PRE_THINK 状态下的 token
     * 支持标签：<think>, <thinking>, <phase>
     */
    private fun handlePreThink(
        token: XmlStreamScanner.Token,
        context: ParserContext,
        messageId: String,
    ): List<SemanticEvent> {
        return when (token) {
            is XmlStreamScanner.TagOpen -> {
                Timber.d("[$messageId] PRE_THINK状态标签开始: ${token.name}")

                // 状态转换逻辑
                when (token.name.lowercase()) {
                    "thinking" -> {
                        context.state = ParserState.THINKING
                        Timber.d("[$messageId] 状态切换: PRE_THINK → THINKING")
                    }
                }

                listOf<SemanticEvent>(SemanticEvent.TagOpened(token.name, token.attributes))
            }
            is XmlStreamScanner.TagClose -> {
                Timber.d("[$messageId] PRE_THINK状态标签结束: ${token.name}")
                listOf<SemanticEvent>(SemanticEvent.TagClosed(token.name))
            }
            is XmlStreamScanner.Text -> {
                if (token.content.isNotBlank()) {
                    listOf<SemanticEvent>(SemanticEvent.TextChunk(token.content))
                } else {
                    emptyList<SemanticEvent>()
                }
            }
        }
    }

    /**
     * 处理 THINKING 状态下的 token
     * 支持标签：<phase>, <title>, <final>, </thinking>
     */
    private fun handleThinking(
        token: XmlStreamScanner.Token,
        context: ParserContext,
        messageId: String,
    ): List<SemanticEvent> {
        return when (token) {
            is XmlStreamScanner.TagOpen -> {
                Timber.d("[$messageId] THINKING状态标签开始: ${token.name}")
                listOf<SemanticEvent>(SemanticEvent.TagOpened(token.name, token.attributes))
            }
            is XmlStreamScanner.TagClose -> {
                Timber.d("[$messageId] THINKING状态标签结束: ${token.name}")

                // 状态转换逻辑
                when (token.name.lowercase()) {
                    "thinking" -> {
                        context.state = ParserState.POST_FINAL
                        Timber.d("[$messageId] 状态切换: THINKING → POST_FINAL")
                    }
                }

                listOf<SemanticEvent>(SemanticEvent.TagClosed(token.name))
            }
            is XmlStreamScanner.Text -> {
                if (token.content.isNotBlank()) {
                    listOf<SemanticEvent>(SemanticEvent.TextChunk(token.content))
                } else {
                    emptyList<SemanticEvent>()
                }
            }
        }
    }

    /**
     * 处理 POST_FINAL 状态下的 token
     * 支持标签：<final>, </final>
     */
    private fun handlePostFinal(
        token: XmlStreamScanner.Token,
        context: ParserContext,
        messageId: String,
    ): List<SemanticEvent> {
        return when (token) {
            is XmlStreamScanner.TagOpen -> {
                Timber.d("[$messageId] POST_FINAL状态标签开始: ${token.name}")
                listOf<SemanticEvent>(SemanticEvent.TagOpened(token.name, token.attributes))
            }
            is XmlStreamScanner.TagClose -> {
                Timber.d("[$messageId] POST_FINAL状态标签结束: ${token.name}")
                listOf<SemanticEvent>(SemanticEvent.TagClosed(token.name))
            }
            is XmlStreamScanner.Text -> {
                if (token.content.isNotBlank()) {
                    listOf<SemanticEvent>(SemanticEvent.TextChunk(token.content))
                } else {
                    emptyList<SemanticEvent>()
                }
            }
        }
    }
}
