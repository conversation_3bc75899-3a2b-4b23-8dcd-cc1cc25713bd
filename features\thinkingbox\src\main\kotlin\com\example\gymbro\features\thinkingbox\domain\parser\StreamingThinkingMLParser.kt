package com.example.gymbro.features.thinkingbox.domain.parser

import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.internal.constants.ThinkingBoxStrings
import kotlinx.coroutines.flow.Flow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * StreamingThinkingMLParser - 三态状态机解析器
 *
 * 🎯 核心设计目标:
 * - 清晰的三态状态机：PRE_THINK / THINKING / POST_FINAL
 * - 上下文感知的标签处理：根据状态不同处理相同标签
 * - 优化的XML解析逻辑：减少复杂度，提升性能
 * - 完全无状态：每次调用都是独立的解析过程
 */
@Singleton
class StreamingThinkingMLParser @Inject constructor(
    private val xmlScanner: XmlStreamScanner,
    private val functionCallDetector: FunctionCallDetector,
) {

    /**
     * 解析器状态枚举
     */
    enum class ParserState {
        PRE_THINK, // <think> 预思考阶段
        THINKING, // <thinking><phase> 正式思考阶段
        POST_FINAL, // <final> 最终输出阶段
    }

    /**
     * 解析器上下文 - 轻量级状态容器
     */
    data class ParserContext(
        var state: ParserState = ParserState.PRE_THINK,
        var currentPhaseId: String? = null,
        var pendingContent: StringBuilder = StringBuilder(),
        // 🔥 新增：Function Call检测上下文
        var functionCallContext: FunctionCallDetector.DetectionContext? = null,
        // 🔥 【Title修复】添加title标签状态跟踪
        var inTitleTag: Boolean = false,
        // 🔥 【Title缓冲迁移】从DomainMapper迁移title缓冲功能
        var titleBuffer: StringBuilder = StringBuilder(),
    )

    /**
     * 主解析方法 - 无状态的流式解析
     *
     * @param messageId 消息ID，用于日志追踪
     * @param tokens 输入的token流
     * @param onEvent 语义事件回调
     */
    suspend fun parseTokenStream(
        messageId: String,
        tokens: Flow<String>,
        onEvent: suspend (SemanticEvent) -> Unit,
    ) {
        val context = ParserContext()
        // 🔥 初始化Function Call检测上下文
        context.functionCallContext = functionCallDetector.createContext()
        val TAG = "TB-PARSER"
        var isFirstToken = true // 🔥 【延迟优化】跟踪是否是第一个token

        // 开始解析

        try {
            tokens.collect { chunk ->
                Timber.tag(TAG).w("🚨 [perthink调试] [$messageId] 收到chunk: '${chunk.take(100)}...'")

                // 🔥 【perthink调试】检查chunk中是否包含<think>标签
                if (chunk.contains("<think>") || chunk.contains("think")) {
                    Timber.tag(TAG).w("🚨 [perthink调试] [$messageId] 检测到think相关内容: '$chunk'")
                }

                // 🔥 【修复1】移除直接发送PreThinkChunk逻辑，让XML标签正常解析
                // 让<think>标签通过正常的XML解析流程处理，避免事件流混乱

                // 使用XML扫描器处理chunk
                val xmlTokens = xmlScanner.feed(chunk)
                Timber.tag(TAG).w("🚨 [perthink调试] [$messageId] XML扫描器输出${xmlTokens.size}个token: ${xmlTokens.map { it::class.simpleName }}")

                // 处理每个XML token
                xmlTokens.forEach { token ->
                    Timber.tag(TAG).w("🚨 [perthink调试] [$messageId] 处理XML token: $token")
                    val events = processXmlToken(token, context, messageId)
                    Timber.tag(TAG).w("🚨 [perthink调试] [$messageId] 生成${events.size}个事件: ${events.map { it::class.simpleName }}")
                    events.forEach { event ->
                        Timber.tag(TAG).w("🚨 [perthink调试] [$messageId] 发送事件: $event")
                        onEvent(event)
                    }
                }
            }

            // 流结束时的清理工作
            val finalEvents = handleStreamEnd(context, messageId)
            finalEvents.forEach { onEvent(it) }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "[$messageId] Parsing error")
            throw e
        }
    }

    /**
     * 处理单个XML token，根据当前状态进行不同的处理
     */
    private fun processXmlToken(
        token: XmlStreamScanner.Token,
        context: ParserContext,
        messageId: String,
    ): List<SemanticEvent> {
        return when (context.state) {
            ParserState.PRE_THINK -> handlePreThink(token, context, messageId)
            ParserState.THINKING -> handleThinking(token, context, messageId)
            ParserState.POST_FINAL -> handlePostFinal(token, context, messageId)
        }
    }

    /**
     * 处理PRE_THINK状态的token - 简化版
     * 🔥 【统一修复】简化状态机逻辑，消除重复事件发送
     */
    private fun handlePreThink(
        token: XmlStreamScanner.Token,
        context: ParserContext,
        messageId: String,
    ): List<SemanticEvent> {
        return when (token) {
            is XmlStreamScanner.TagOpen -> {
                // 🔥 【加强解析策略】严格检查标签名，拒绝包含冒号的非标准标签
                if (token.name.contains(":")) {
                    Timber.tag("TB-PARSER").d("[$messageId] 忽略非标准标签（包含冒号）: ${token.name}")
                    return emptyList()
                }

                when (token.name.lowercase()) {
                    "think" -> {
                        // 🔥 【唯一接口修复】<think>标签直接创建PhaseStart，去掉中间转换层
                        context.currentPhaseId = "perthink"
                        val phaseStartEvent = SemanticEvent.PhaseStart("perthink", ThinkingBoxStrings.PERTHINK_TITLE)
                        Timber.tag("TB-PARSER").i("[$messageId] perthink开始")
                        listOf(phaseStartEvent)
                    }

                    "thinking" -> {
                        // 🔥 【简化状态切换】直接从PRE_THINK切换到THINKING
                        finalizePerthinkAndSwitchToThinking(context, messageId)
                    }

                    "phase" -> {
                        // 🔥 【简化phase处理】只处理有id的正式phase
                        val phaseId = token.attributes["id"]
                        if (phaseId != null && phaseId != "perthink") {
                            // 正式phase开始，先结束perthink再切换到THINKING状态
                            val events = finalizePerthinkAndSwitchToThinking(
                                context,
                                messageId,
                            ).toMutableList()
                            context.currentPhaseId = phaseId
                            events.add(SemanticEvent.PhaseStart(phaseId, null))
                            events
                        } else {
                            emptyList() // 忽略无效或hint类型的phase标签
                        }
                    }

                    else -> {
                        Timber.tag("TB-PARSER").d("[$messageId] 忽略PRE_THINK状态下的标签: ${token.name}")
                        emptyList()
                    }
                }
            }

            is XmlStreamScanner.TagClose -> {
                when (token.name.lowercase()) {
                    "think" -> {
                        // 🔥 【唯一接口修复】</think>标签直接发送PreThinkEnd和PhaseEnd，去掉中间转换层
                        if (context.currentPhaseId == "perthink") {
                            context.currentPhaseId = null
                            listOf(
                                SemanticEvent.PreThinkEnd,
                                SemanticEvent.PhaseEnd("perthink")
                            )
                        } else {
                            emptyList()
                        }
                    }
                    else -> emptyList()
                }
            }

            is XmlStreamScanner.Text -> {
                // 🔥 【简化文本处理】只处理perthink内容和Function Call检测
                val cleanedText = cleanPhaseHints(token.content)
                val events = mutableListOf<SemanticEvent>()

                if (cleanedText.isNotEmpty() && context.currentPhaseId == "perthink") {
                    // 发送perthink内容
                    events.add(SemanticEvent.PreThinkChunk(cleanedText))
                    events.add(SemanticEvent.PhaseContent("perthink", cleanedText))
                }

                // Function Call检测
                events.addAll(detectFunctionCallInText(cleanedText, context, messageId))
                events
            }
        }
    }

    /**
     * 🔥 【新增辅助方法】结束perthink并切换到THINKING状态
     * 消除重复的状态切换逻辑
     */
    private fun finalizePerthinkAndSwitchToThinking(
        context: ParserContext,
        messageId: String,
    ): List<SemanticEvent> {
        val events = mutableListOf<SemanticEvent>()

        // 如果当前在perthink状态，先结束它
        if (context.currentPhaseId == "perthink") {
            Timber.tag("TB-PARSER").d("[$messageId] 结束perthink phase并切换到THINKING状态")
            events.add(SemanticEvent.PreThinkEnd)
            events.add(SemanticEvent.PhaseEnd("perthink"))
            context.currentPhaseId = null
        }

        // 切换到THINKING状态
        context.state = ParserState.THINKING
        Timber.tag("TB-PARSER").d("[$messageId] 状态切换: PRE_THINK → THINKING")

        return events
    }

    /**
     * 清洗预思考内容中的XML标签
     * 🔥 【修复3】清洗<think>标签和<phase:PLAN>类型标签
     * 🔥 【增强清洗】处理所有可能的phase标签格式变体
     */
    private fun cleanPhaseHints(text: String): String {
        var cleanedText = text

        // 🔥 【基础清洗】移除think标签
        cleanedText = cleanedText
            .replace(Regex("<think>", RegexOption.IGNORE_CASE), "")
            .replace(Regex("</think>", RegexOption.IGNORE_CASE), "")

        // 🔥 【增强清洗】移除所有phase:XXX格式的标签（开标签和闭标签）
        cleanedText = cleanedText
            .replace(Regex("<phase:[^>]*>", RegexOption.IGNORE_CASE), "")
            .replace(Regex("</phase:[^>]*>", RegexOption.IGNORE_CASE), "")

        // 🔥 【额外清洗】移除可能的裸phase标记（如 "phase:PLAN"）
        cleanedText = cleanedText
            .replace(Regex("\\bphase\\s*:\\s*[A-Za-z_]+\\b", RegexOption.IGNORE_CASE), "")

        // 🔥 【调试日志】记录清洗效果
        if (text != cleanedText) {
            Timber.tag("TB-PARSER").d("🧹 perthink清洗: '${text.take(50)}...' → '${cleanedText.take(50)}...'")
        }

        return cleanedText.trim()
    }

    /**
     * 处理THINKING状态的token - 简化版
     * 🔥 【统一修复】简化正式思考阶段的标签处理逻辑
     */
    private fun handleThinking(
        token: XmlStreamScanner.Token,
        context: ParserContext,
        messageId: String,
    ): List<SemanticEvent> {
        return when (token) {
            is XmlStreamScanner.TagOpen -> {
                // 🔥 【加强解析策略】严格检查标签名，拒绝包含冒号的非标准标签
                if (token.name.contains(":")) {
                    Timber.tag("TB-PARSER").d("[$messageId] 忽略非标准标签（包含冒号）: ${token.name}")
                    return emptyList()
                }

                when (token.name.lowercase()) {
                    "thinking" -> {
                        // thinking标签开始，无需特殊处理
                        Timber.tag("TB-PARSER").d("[$messageId] <thinking>标签开始")
                        emptyList()
                    }

                    "phase" -> {
                        // 🔥 【加强解析策略】只有phase id="..."格式才被处理为正式phase
                        val phaseId = token.attributes["id"]
                        if (phaseId != null && phaseId.isNotBlank()) {
                            // 正式phase：必须有有效的id属性
                            val title = token.attributes["title"] // 可能为null，等待<title>标签
                            context.currentPhaseId = phaseId
                            Timber.tag("TB-PARSER").d("[$messageId] 正式phase开始: id=$phaseId")
                            listOf(SemanticEvent.PhaseStart(phaseId, title))
                        } else {
                            // 忽略没有id属性的phase标签（如<phase:PLAN>）
                            Timber.tag("TB-PARSER").d("[$messageId] 忽略无id的phase标签: ${token.name}")
                            emptyList()
                        }
                    }

                    "title" -> {
                        // 🔥 【Title缓冲迁移】title标签开始，清空缓冲区准备接收
                        context.inTitleTag = true
                        context.titleBuffer.clear()
                        emptyList() // 不发送TagOpened事件，直接在这里处理
                    }

                    "final" -> {
                        // 🔥 【双时序核心】<final>标签只负责后台数据缓存，UI显示由动画接口控制
                        context.state = ParserState.POST_FINAL
                        context.pendingContent.clear()

                        Timber.tag("TB-PARSER").i("[$messageId] final开始")

                        listOf(
                            SemanticEvent.TagOpened("final", token.attributes),
                            SemanticEvent.FinalStart, // 🔥 【后台数据】开始缓存富文本内容
                        )
                    }

                    else -> {
                        // 其他标签保持原样传递
                        listOf(SemanticEvent.TagOpened(token.name, token.attributes))
                    }
                }
            }

            is XmlStreamScanner.TagClose -> {
                // 🔥 【加强解析策略】严格检查标签名，拒绝包含冒号的非标准标签
                if (token.name.contains(":")) {
                    Timber.tag("TB-PARSER").d("[$messageId] 忽略非标准闭合标签（包含冒号）: ${token.name}")
                    return emptyList()
                }

                when (token.name.lowercase()) {
                    "phase" -> {
                        // 🔥 【简化phase结束】phase结束处理
                        if (context.currentPhaseId != null) {
                            val phaseId = context.currentPhaseId!!
                            Timber.tag("TB-PARSER").d("[$messageId] Phase结束: $phaseId")
                            context.currentPhaseId = null
                            listOf(SemanticEvent.PhaseEnd(phaseId))
                        } else {
                            emptyList()
                        }
                    }

                    "title" -> {
                        // 🔥 【Title缓冲迁移】title标签结束，发送完整标题更新
                        context.inTitleTag = false
                        val completeTitle = context.titleBuffer.toString()
                        val phaseId = context.currentPhaseId

                        if (phaseId != null && completeTitle.isNotBlank()) {
                            listOf(SemanticEvent.PhaseTitleUpdate(phaseId, completeTitle))
                        } else {
                            emptyList()
                        }
                    }

                    "thinking" -> {
                        // 🔥 【双时序核心】</thinking>标签只处理后台数据结束，不触发UI切换
                        val events = mutableListOf<SemanticEvent>()

                        // 🔥 【后台数据时序】如果当前有激活的phase，标记数据完成
                        if (context.currentPhaseId != null) {
                            val phaseId = context.currentPhaseId!!
                            events.add(SemanticEvent.PhaseEnd(phaseId))
                            // 🔥 【关键】不清空currentPhaseId，让UI动画接口控制切换
                        }

                        // 🔥 【后台数据结束】发送TagClosed事件，标记数据流结束
                        events.add(SemanticEvent.TagClosed("thinking"))

                        // 🔥 【状态转换】切换到POST_FINAL状态，等待<final>标签
                        context.state = ParserState.POST_FINAL
                        Timber.tag("TB-PARSER").i("[$messageId] thinking结束")

                        events.toList()
                    }

                    else -> {
                        listOf(SemanticEvent.TagClosed(token.name))
                    }
                }
            }

            is XmlStreamScanner.Text -> {
                val events = mutableListOf<SemanticEvent>()

                // 🔥 【Title缓冲迁移】根据context生成对应事件
                if (context.inTitleTag) {
                    // title标签内的文本 - 缓冲而不立即发送事件
                    context.titleBuffer.append(token.content)
                    // 不添加任何事件，等待</title>标签时统一处理
                } else if (context.currentPhaseId != null) {
                    // phase内容文本
                    events.add(SemanticEvent.PhaseContent(context.currentPhaseId!!, token.content))
                } else {
                    // 兜底处理
                    events.add(SemanticEvent.TextChunk(token.content))
                }

                // Function Call检测
                events.addAll(detectFunctionCallInText(token.content, context, messageId))
                events
            }
        }
    }

    /**
     * 处理POST_FINAL状态的token - 简化版
     * 🔥 【统一修复】简化final阶段处理，确保流式渲染效果
     */
    private fun handlePostFinal(
        token: XmlStreamScanner.Token,
        context: ParserContext,
        messageId: String,
    ): List<SemanticEvent> {
        return when (token) {
            is XmlStreamScanner.TagOpen -> {
                // POST_FINAL状态下忽略所有开始标签
                Timber.tag("TB-PARSER").d("[$messageId] POST_FINAL状态忽略标签: ${token.name}")
                emptyList()
            }

            is XmlStreamScanner.TagClose -> {
                when (token.name.lowercase()) {
                    "final" -> {
                        // 🔥 【简化final结束】</final>标签结束，发送FinalEnd事件
                        Timber.tag("TB-PARSER").i("[$messageId] </final>标签结束，发送FinalEnd事件")
                        listOf(SemanticEvent.FinalEnd)
                    }
                    else -> emptyList()
                }
            }

            is XmlStreamScanner.Text -> {
                // 🔥 【简化final内容】直接发送FinalChunk事件，实现流式渲染
                val events = mutableListOf<SemanticEvent>()

                if (token.content.isNotEmpty()) {
                    events.add(SemanticEvent.FinalChunk(token.content))
                }

                // Function Call检测
                events.addAll(detectFunctionCallInText(token.content, context, messageId))
                events
            }
        }
    }

    /**
     * 处理流结束 - 简化版
     * 🔥 【防重复Final】简化流结束处理，确保事件唯一性
     */
    private fun handleStreamEnd(context: ParserContext, messageId: String): List<SemanticEvent> {
        val events = mutableListOf<SemanticEvent>()

        // 🔥 【简化边界处理】处理未完成的phase
        when (context.state) {
            ParserState.PRE_THINK -> {
                if (context.currentPhaseId == "perthink") {
                    Timber.tag("TB-PARSER").d("[$messageId] 流结束时完成perthink phase")
                    events.add(SemanticEvent.PreThinkEnd)
                    events.add(SemanticEvent.PhaseEnd("perthink"))
                }
            }

            ParserState.THINKING -> {
                if (context.currentPhaseId != null) {
                    Timber.tag("TB-PARSER").d("[$messageId] 流结束时完成phase: ${context.currentPhaseId}")
                    events.add(SemanticEvent.PhaseEnd(context.currentPhaseId!!))
                }
            }

            ParserState.POST_FINAL -> {
                // 🔥 【关键修复】移除FinalArrived事件，依赖纯流式系统
                // 新的流式系统：FinalStart → FinalChunk → FinalEnd
                // 不再发送FinalArrived，避免与StreamingFinalRenderer冲突
                Timber.tag("TB-PARSER").i("[$messageId] 流结束时在POST_FINAL状态，依赖流式系统处理")
            }
        }

        // 🔥 【统一结束】发送流结束事件
        events.add(SemanticEvent.StreamFinished())

        Timber.tag("TB-PARSER").d("[$messageId] 流结束处理完成，状态: ${context.state}, 事件数: ${events.size}")
        return events
    }

    /**
     * 🔥 新增：在文本中检测Function Call
     *
     * @param textContent 文本内容
     * @param context 解析器上下文
     * @param messageId 消息ID
     * @return 检测到的SemanticEvent列表
     */
    private fun detectFunctionCallInText(
        textContent: String,
        context: ParserContext,
        messageId: String,
    ): List<SemanticEvent> {
        val functionCallContext = context.functionCallContext ?: return emptyList()

        return try {
            functionCallDetector.detectInChunk(textContent, functionCallContext, messageId)
        } catch (e: Exception) {
            Timber.tag("TB-PARSER").w(e, "[$messageId] Function call detection failed")
            emptyList()
        }
    }
}
