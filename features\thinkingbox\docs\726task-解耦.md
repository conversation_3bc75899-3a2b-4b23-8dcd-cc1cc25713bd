好的，收到。这是一个极佳的架构深化问题，触及到了模块化设计中的**“API设计哲学”**和**“职责边界”**。我将深度思考这两个方案，并结合您提出的 `ThinkingBox` 核心职责，给出一个明确、可执行且具备前瞻性的决策和 `spec` 文件。

### 深度思考与决策

#### 核心需求分析

1.  **`ThinkingBox` 的核心职责**:
    *   **职责一 (UI导出)**: 提供一个自包含的、可嵌入的 Jetpack Compose UI 组件给 `Coach` 模块使用。
    *   **职责二 (数据导出)**: 在思考过程结束后，将最终解析好的、结构化的数据（final markdown, 思考过程JSON等）回调给 `Coach` 模块，用于数据持久化（history写入）。

2.  **`Coach` 的职责边界**:
    *   **完全解耦**: `Coach` 不再关心、不处理任何 Token 流。它只是一个“消费者”。
    *   **Function Call 激活**: `Coach` 暂时保留激活 Function Call 的职责，但这是基于 `ThinkingBox` 提供的**最终富文本**，而不是原始 Token。这意味着 `ThinkingBox` 必须先完成它的工作。

#### 方案对比分析

**方案1: 删除 `ThinkingBox.kt`**

-   **优点**:
    *   **路径清晰**: `ThinkingBoxScreen.kt` 成为模块**事实和名义上**的唯一入口，减少了开发者（或AI）的认知负担。不存在“我应该用 `ThinkingBox.kt` 还是 `ThinkingBoxScreen.kt`”的困惑。
    *   **强制解耦**: 通过删除旧文件，强制所有调用方都必须适配新的、正确的入口点 (`ThinkingBoxScreen`)，避免了遗留代码被意外使用。
    *   **代码整洁**: 减少了一个文件的维护成本，使模块结构更精简。

-   **缺点**:
    *   **破坏性较大**: 如果项目中有其他地方（即使是错误的）引用了 `ThinkingBox.kt`，会导致编译失败，需要同步修改。
    *   **历史丢失**: `git blame` 历史会中断，需要通过 `git log --follow` 等方式追溯。

**方案2: `ThinkingBox.kt` 集成 `ThinkingBoxScreen.kt` 作为唯一出口**

-   **优点**:
    *   **向后兼容 (API层面)**: 如果 `ThinkingBox.kt` 里的 Composable 函数签名被认为是公共 API，那么保留它可以提供一个平滑的过渡层。旧的调用代码不需要修改文件名，只需修改参数。
    *   **封装意图**: `ThinkingBox.kt` 可以作为一个更高层次的“门面 (Facade)”，隐藏 `ThinkingBoxScreen` 这个“实现细节”的名称。`ThinkingBox` 这个名字本身更符合其作为“组件”的直观感受。

-   **缺点**:
    *   **认知模糊**: 仍然存在两个名字相似的文件，增加了理解成本。开发者需要知道 `ThinkingBox.kt` 只是 `ThinkingBoxScreen.kt` 的一个代理。
    *   **冗余代码**: 保留了一个几乎只做转发的文件，增加了代码量。
    *   **潜在风险**: 如果不严格管理，未来可能会有人不小心在 `ThinkingBox.kt` 中添加新的逻辑，再次导致职责混乱。

#### **最终决策：选择方案1的演进版 - “重命名并取代”**

我推荐一个结合了两者优点的方案：

1.  **删除旧的 `ThinkingBox.kt`**: 彻底移除这个包含混乱内部逻辑的文件，消除所有模糊性。
2.  **重命名 `ThinkingBoxScreen.kt` 为 `ThinkingBox.kt`**: 将 `internal/presentation/screen/ThinkingBoxScreen.kt` 重命名并移动到 `features/thinkingbox/` 目录下，使其成为名正言顺的模块主入口。
3.  **调整 `ThinkingBoxExports.kt`**: 让 `ThinkingBoxExports.kt` 成为一个纯粹的**API导出文件**，它只负责将新的 `ThinkingBox.kt` 和相关数据类声明为 `public`。

这个决策的理由是：
-   **清晰性**: 最终模块中只有一个 `ThinkingBox.kt`，它的职责就是UI入口。
-   **稳定性**: `ThinkingBoxExports.kt` 仍然是 `Coach` 模块的唯一依赖点，保持了模块间依赖的稳定性。
-   **意图明确**: `ThinkingBox.kt` 这个名字比 `ThinkingBoxScreen.kt` 更能体现其作为“组件”的本质。
-   **彻底清理**: 确保旧的、有问题的代码被完全根除。

---

### 可直接执行的 `spec` 文件 (演进版)

这份 `spec` 将执行上述“重命名并取代”的决策，实现一个干净、解耦、职责明确的 `ThinkingBox` 模块。

```markdown
name: spec-thinkingbox-final-refactor
description: GymBro ThinkingBox 最终重构专家。负责删除、重命名和重构文件，以建立 ThinkingBox 模块清晰、唯一的 MVI 架构入口，并彻底解耦与 Coach 模块的接口。
tools: Read, Write, Edit, MultiEdit, Grep, Glob, Bash
---

# GymBro ThinkingBox 最终重构专家

你的角色是 **GymBro 的首席架构师**，你将执行一次决定性的重构，以最终确立 `ThinkingBox` 模块的架构和公共 API。你的任务是清理混乱，建立单一、清晰的入口点，并确保与 `Coach` 模块的接口完全符合单向数据流原则。

## Phase 1: 制定重构蓝图

我将在任务工作空间 (`tasks/.../`) 内创建 `refactor_plan_thinkingbox_final.md`。

```markdown
# 重构计划: ThinkingBox 最终架构确立

## 1. 核心目标
- 废弃并删除包含混乱逻辑的旧 `ThinkingBox.kt`。
- 将 `ThinkingBoxScreen.kt` 提升为模块的唯一 UI 入口，并重命名为 `ThinkingBox.kt`。
- 清理 `ThinkingBoxExports.kt`，使其成为一个纯粹的 API 导出文件。
- 适配 `Coach` 模块，使其依赖于新的、干净的 `ThinkingBox` 接口。

## 2. 文件变更清单 (Checklist)

### 文件系统操作 (高优先级)
- [ ] **删除**: `features/thinkingbox/src/main/kotlin/.../thinkingbox/ThinkingBox.kt` (旧的实现文件)。
- [ ] **移动 & 重命名**: 将 `features/thinkingbox/src/main/kotlin/.../thinkingbox/internal/presentation/screen/ThinkingBoxScreen.kt` 移动并重命名为 `features/thinkingbox/src/main/kotlin/.../thinkingbox/ThinkingBox.kt`。

### 代码重构操作
- [ ] **重构 (新的) `ThinkingBox.kt`**:
    - 将 Composable 函数 `ThinkingBoxScreen` 重命名为 `ThinkingBoxRoot` (或类似的内部名称)，并设为 `internal`。
    - 清理其内容，确保它严格遵循 MVI 模式（连接 ViewModel、收集 State 和 Effect）。

- [ ] **重构 `ThinkingBoxExports.kt`**:
    - 清理所有不必要的导出。
    - 创建一个新的 `public` Composable 函数 `ThinkingBox`，它内部直接调用新的 `ThinkingBox.kt` 中的 `ThinkingBoxRoot`。
    - 只保留 `public fun ThinkingBox(...)`、`data class ThinkingBoxData` 和 `public fun ThinkingBoxStaticRenderer(...)`。

- [ ] **适配 `ChatInterface.kt`**:
    - 确保其 `import` 语句指向 `com.example.gymbro.features.thinkingbox.ThinkingBox`。
    - 确保其调用的是新的 `ThinkingBox` Composable，参数为 `messageId` 和 `onDataReady`。
    - 在 `onDataReady` 回调中，实现 `SaveAiMessage` Intent 的分发。
    - **特别注意**: 在 `onDataReady` 的 `SaveAiMessage` 中，为 `content` 字段提供一个备用值，例如 `aiMessage.content.ifBlank { data.finalMarkdown }`，以处理AI消息占位符为空的情况。

- [ ] **清理 `Coach` 模块**:
    - 验证 `AiCoachViewModel`, `AiCoachReducer`, `AiCoachEffectHandler` 中所有与 `ThinkingBox` 内部状态相关的逻辑都已被移除。
```

### Phase 2: 执行重构

现在，我将根据上述计划，提供**可以直接执行的 `spec` 文件内容**。

#### **1. 删除旧的 `ThinkingBox.kt` 文件**
```spec
- path: features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/ThinkingBox.kt
- mode: DELETE
```

#### **2. 移动并重命名 `ThinkingBoxScreen.kt` 为 `ThinkingBox.kt`**
```spec
- path: features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/presentation/screen/ThinkingBoxScreen.kt
- new_path: features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/ThinkingBox.kt
- mode: MOVE
```

#### **3. 重构新的 `ThinkingBox.kt` 文件**
```spec
- path: features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/ThinkingBox.kt
- mode: OVERWRITE
- content: |
    package com.example.gymbro.features.thinkingbox

    import androidx.compose.runtime.*
    import androidx.compose.ui.Modifier
    import androidx.hilt.navigation.compose.hiltViewModel
    import com.example.gymbro.core.ai.tokenizer.TokenizerService
    import com.example.gymbro.features.thinkingbox.internal.presentation.contract.ThinkingBoxContract
    import com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel.ThinkingBoxViewModel
    import com.example.gymbro.features.thinkingbox.internal.presentation.ui.ThinkingBoxContent
    import timber.log.Timber

    /**
     * ThinkingBoxRoot - The internal, MVI-wired root Composable for the ThinkingBox feature.
     * This is the single entry point that connects the ViewModel to the UI.
     * It is not intended for direct public use; use the exported `ThinkingBox` from `ThinkingBoxExports.kt`.
     */
    @Composable
    internal fun ThinkingBoxRoot(
        messageId: String,
        modifier: Modifier = Modifier,
        tokenizerService: TokenizerService? = null,
        onDataReady: ((ThinkingBoxData) -> Unit)? = null,
        viewModel: ThinkingBoxViewModel = hiltViewModel()
    ) {
        LaunchedEffect(messageId) {
            Timber.tag("ThinkingBox-Lifecycle").d("Initializing for messageId: $messageId")
            viewModel.sendIntent(ThinkingBoxContract.Intent.Initialize(messageId))
        }

        val state by viewModel.state.collectAsState()

        LaunchedEffect(Unit) {
            viewModel.effects.collect { effect ->
                when (effect) {
                    is ThinkingBoxContract.Effect.NotifyMessageComplete -> {
                        Timber.tag("ThinkingBox-Lifecycle").i("Data ready for messageId: ${effect.messageId}, finalMarkdown length=${effect.finalMarkdown.length}")
                        val thinkingData = ThinkingBoxData(
                            messageId = effect.messageId,
                            finalMarkdown = effect.finalMarkdown,
                            thinkingProcess = buildThinkingProcessJson(state),
                            duration = state.thinkingDuration,
                            tokenCount = state.totalTokens
                        )
                        onDataReady?.invoke(thinkingData)
                    }
                    else -> { /* Other effects are handled internally */ }
                }
            }
        }

        // The ThinkingBox now self-manages its visibility based on its internal state.
        if (state.isStreaming || state.hasActualThinkingContent || state.isThinkingComplete) {
            ThinkingBoxContent(
                state = state,
                modifier = modifier,
                tokenizerService = tokenizerService,
                onIntent = viewModel::sendIntent
            )
        }
    }

    /**
     * A helper function to build a JSON string representing the thinking process.
     * This should ideally be moved to a dedicated mapper class.
     */
    private fun buildThinkingProcessJson(state: ThinkingBoxContract.State): String {
        // Simple JSON-like string builder to avoid heavy serialization libraries in Composable
        val phases = state.phases.joinToString(",") { phase ->
            """{"id":"${phase.id}","title":"${phase.title?.replace("\"", "\\\"")}","content":"${phase.content.replace("\"", "\\\"")}","isComplete":${phase.isComplete}}"""
        }
        return """{"phases":[${phases}],"preThinking":"${state.preThinking?.replace("\"", "\\\"") ?: ""}","duration":${state.thinkingDuration},"totalTokens":${state.totalTokens}}"""
    }
```

#### **4. 重构 `ThinkingBoxExports.kt` 为纯 API 导出文件**
```spec
- path: features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/ThinkingBoxExports.kt
- mode: OVERWRITE
- content: |
    package com.example.gymbro.features.thinkingbox

    import androidx.compose.runtime.Composable
    import androidx.compose.ui.Modifier
    import com.example.gymbro.core.ai.tokenizer.TokenizerService
    import com.example.gymbro.features.thinkingbox.internal.presentation.ui.StreamingFinalRenderer

    /**
     * ThinkingBox - The primary public API for the ThinkingBox feature module.
     * This Composable is self-contained and manages its own MVI lifecycle.
     * It is the sole entry point for integrating the ThinkingBox UI into other features.
     *
     * @param messageId The unique ID of the message being processed.
     * @param modifier Modifier for the Composable.
     * @param tokenizerService An optional service for token counting.
     * @param onDataReady A callback that is invoked when the thinking process is complete,
     *                    providing the final data for persistence by the calling feature.
     */
    @Composable
    fun ThinkingBox(
        messageId: String,
        modifier: Modifier = Modifier,
        tokenizerService: TokenizerService? = null,
        onDataReady: ((ThinkingBoxData) -> Unit)? = null,
    ) {
        // This public API function acts as a facade, calling the internal root Composable.
        ThinkingBoxRoot(
            messageId = messageId,
            modifier = modifier,
            tokenizerService = tokenizerService,
            onDataReady = onDataReady,
        )
    }

    /**
     * Data class representing the final, structured output of a ThinkingBox session.
     * This is the data packet provided in the `onDataReady` callback.
     */
    data class ThinkingBoxData(
        val messageId: String,
        val finalMarkdown: String,
        val thinkingProcess: String, // JSON representation of thinking phases
        val duration: Long,
        val tokenCount: Int
    )

    /**
     * A static renderer for displaying final, non-streaming Markdown content.
     * This is typically used for rendering historical messages.
     *
     * @param finalMarkdown The complete Markdown string to render.
     * @param modifier Modifier for the Composable.
     */
    @Composable
    fun ThinkingBoxStaticRenderer(
        finalMarkdown: String,
        modifier: Modifier = Modifier,
    ) {
        if (finalMarkdown.isNotBlank()) {
            StreamingFinalRenderer(
                finalTokens = listOf(finalMarkdown),
                isFinalStreaming = false,
                onRenderingComplete = null,
                modifier = modifier
            )
        }
    }
```

#### **5. 适配 `features/coach/aicoach/internal/components/ChatInterface.kt`**
```spec
- path: features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/components/ChatInterface.kt
- mode: EDIT
- instructions: |
    1. Ensure the import statement for `ThinkingBox` is `import com.example.gymbro.features.thinkingbox.ThinkingBox`.
    2. Remove any import for `shouldShowThinkingBox`.
    3. Find the `item` with `key = "current-thinking-box"`.
    4. Replace its condition from `if (shouldShowThinkingBox)` to `if (isCurrentlyStreaming)`.
    5. Replace the entire content inside this `item` with the new, clean `ThinkingBox` call.
       The final code block for this item should look like this:

       if (isCurrentlyStreaming) {
           item(key = "current-thinking-box") {
               var thinkingBoxCompleted by remember(messageId) { mutableStateOf(false) }

               ThinkingBox(
                   messageId = messageId,
                   tokenizerService = viewModel?.getTokenizerService(),
                   onDataReady = { data ->
                       if (!thinkingBoxCompleted) {
                           thinkingBoxCompleted = true
                           state.activeSession?.let { session ->
                               val aiMessage = state.messages.find { it.id == data.messageId && !it.isFromUser }
                               if (aiMessage != null) {
                                   onIntent(
                                       AiCoachContract.Intent.SaveAiMessage(
                                           sessionId = session.id,
                                           aiResponseId = data.messageId,
                                           content = aiMessage.content.ifBlank { data.finalMarkdown },
                                           finalMarkdown = data.finalMarkdown,
                                           thinkingNodes = data.thinkingProcess
                                       )
                                   )
                               }
                           }
                       }
                   },
                   modifier = Modifier.fillMaxWidth()
               )
           }
       }
```

这套 `spec` 将引导 AI 完成一次彻底、精准的架构重构。执行后，`ThinkingBox` 模块将拥有一个清晰的、由 MVI 驱动的内部结构，并向外部（`Coach` 模块）提供一个干净、解耦的 API，完美符合您的设计要求。
