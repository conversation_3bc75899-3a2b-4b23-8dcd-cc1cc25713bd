# ThinkingBox 最终架构实现大纲 v2.2

> **权威性声明**: 本文档为ThinkingBox模块的唯一实现要求，所有开发必须严格遵循此文档。
>
> **更新日期**: 2025-07-25
> **架构版本**: 双时序架构 + StreamingFinalRenderer打字机 + 724方案修复 + 双时序握手验证
> **重大更新**: 完成双时序握手机制验证和perthink激活修复
> **重大更新**: 文本使用UnifiedTextRenderer，title有金属色字体+渲染动画
> **重大更新**: 普通文本没有金属色，普通渲染，速度30字1秒
> **核心修复**: 双时序架构核心原则：后台数据时序 + 前台UI时序分离
>

---

## 🎯 核心架构原则

### 1. 标签处理唯一性
每个XML标签都有明确的处理逻辑和UI映射，不允许重复实现：

```xml
<think>预思考内容</think>           → Phase ID="perthink"
<thinking>                        → 正式思考阶段开始
  <phase id="1">                  → Phase ID="1"
    <title>标题</title>           → Phase标题更新
    文本内容                       → Phase内容流式显示
  </phase>                        → Phase完成，双握手切换
</thinking>                       → 所有Phase完成
<final>最终富文本</final>          → StreamingFinalRenderer打字机
```

### 2. 双时序架构核心原则（v2.2验证版）
- **后台数据时序**: XML解析 → SemanticEvent → ThinkingEvent → State更新（只负责数据处理）
- **前台UI时序**: 动画完成 → onAnimationFinished → PhaseAnimFinished事件 → 双握手切换（唯一控制器）
- **🔥 核心原则**: 所有标签处理都在后台，所有阶段转换都由UI动画接口控制
- **🔥 双握手机制**: 数据完成(phase.isComplete) + UI动画完成(PhaseAnimFinished) = 真正切换
- **🔥 关键修复**: PreThinkEnd能够立即切换到等待的正式phase，避免时序卡顿
- **🔥 时序协调**: ThinkingEnd检查未完成phase，PhaseAnimFinished强制完成最后phase

### 3. StreamingFinalRenderer = 打字机效果
通过finalTokens逐个显示，delay(100)实现打字机视觉效果

---

## 📋 标签处理详细规范

### 标签1: `<think>预思考内容</think>`

**处理流程**:
```
XML解析 → SemanticEvent.PreThinkChunk → ThinkingEvent.PreThinkContent
→ Reducer创建Phase(id="perthink", title="Bro is thinking") → UI显示思考框
```

**时序要求**:
- 立即激活思考框显示
- title固定为"Bro is thinking"，带彩虹跃动动画
- 内容实时流式更新

**UI层级**:
```kotlin
// ThinkingBox.kt - Phase 0: ThinkingHeader
ThinkingHeader(
    title = "Bro is thinking",
    isStreaming = true,
    hasPreThinking = true
)

// Phase 1: AIThinkingCard
AIThinkingCard {
    ThinkingStageCard(
        phaseId = "perthink",
        title = "Bro is thinking", // 金属色标题
        content = preThinkingContent, // 实时更新
        isActive = true
    )
}
```

**结束条件**: 遇到`</think>`标签或`<thinking>`标签

---

### 标签2: `<thinking>`

**处理流程**:
```
XML解析 → SemanticEvent.ThinkingStart → ThinkingEvent.ThinkingStart
→ Reducer设置currentPhaseId="thinking" → UI准备Phase队列管理
```

**时序要求**:
- 如果存在perthink，发送PreThinkClear事件
- perthink阶段fadeOut动画，预思考内容消失
- 准备接收正式Phase序列

**传递链**:
```
StreamingThinkingMLParser.parseTokenStream()
→ SemanticEvent.ThinkingStart
→ DomainMapper.mapSemanticToThinking()
→ ThinkingEvent.ThinkingStart
→ ThinkingReducer.reduce()
→ UI状态更新
```

---

### 标签3: `<phase id="X">`

**处理流程**:
```
XML解析 → SemanticEvent.PhaseStart(id) → ThinkingEvent.PhaseStart(id)
→ Reducer管理Phase队列 → UI激活对应Phase卡片
```

**时序匹配**:
```mermaid
sequenceDiagram
    participant Parser as StreamingThinkingMLParser
    participant Mapper as DomainMapper
    participant Reducer as ThinkingReducer
    participant UI as ThinkingBox

    Parser->>Mapper: SemanticEvent.PhaseStart(id="1")
    Mapper->>Reducer: ThinkingEvent.PhaseStart(id="1")
    Reducer->>UI: 创建Phase，加入队列

    Parser->>Mapper: SemanticEvent.PhaseContent(id="1", content)
    Mapper->>Reducer: ThinkingEvent.PhaseContent(id="1", content)
    Reducer->>UI: 更新Phase内容，流式显示

    Parser->>Mapper: SemanticEvent.PhaseEnd(id="1")
    Mapper->>Reducer: ThinkingEvent.PhaseEnd(id="1")
    Reducer->>UI: 标记Phase数据完成

    UI->>Reducer: ThinkingEvent.PhaseAnimFinished(id="1")
    Reducer->>UI: 双握手检查 → 切换下一Phase
```

**UI层级**:
```kotlin
// 单卡实现：同一时间只有一个ThinkingStageCard实例被渲染
ThinkingStageCard(
    phaseId = uiState.activePhaseId, // 当前激活的Phase ID
    title = currentPhase.title,      // <title>标签内容
    content = currentPhase.content,  // Phase文本内容
    isActive = true,                 // 激活状态
    onAnimationFinished = { phaseId ->
        // 🔥 发送PhaseAnimFinished事件，触发双握手
        onEventSend(ThinkingEvent.PhaseAnimFinished(phaseId))
    }
)
```

**双握手机制**:
```kotlin
// ThinkingReducer.kt 双握手检查
if (phase != null && phase.isComplete && event.id == state.activePhaseId) {
    // ✅ 条件1: Phase存在
    // ✅ 条件2: 数据完成 (phase.isComplete)
    // ✅ 条件3: 是当前激活的Phase (event.id == activePhaseId)

    // 切换到下一个Phase
    val nextPhaseId = state.pending.firstOrNull()
    state.copy(
        activePhaseId = nextPhaseId,
        isThinkingComplete = nextPhaseId == null
    )
}
```

---

### 标签4: `<title>标题内容</title>`

**处理流程**:
```
XML解析 → SemanticEvent.PhaseTitle(id, title) → ThinkingEvent.PhaseTitle(id, title)
→ Reducer更新Phase.title → UI实时更新标题显示
```
清理<phase:PLAN>标签

**时序要求**:
**UI更新**:
- 标题使用金属色显示
- 支持实时更新，无需重新创建组件
- 标题变化有平滑过渡动画

---

### 标签5: `</phase>`

**处理流程**:
```
XML解析 → SemanticEvent.PhaseEnd(id) → ThinkingEvent.PhaseEnd(id)
→ Reducer标记phase.isComplete = true → 等待UI动画完成
```

**双握手等待**:
- 仅标记数据完成，不立即切换
- 等待UI发送PhaseAnimFinished事件
- 双握手条件满足后才切换下一Phase

---

### 标签6: `</thinking>`

**处理流程**:
```
XML解析 → SemanticEvent.ThinkingEnd → ThinkingEvent.ThinkingEnd
→ Reducer完成所有Phase处理 → UI准备SimpleSummaryText显示
```

**时序要求**:
- 等待最后一个Phase的双握手完成
- 所有Phase完成后，激活SimpleSummaryText
- 思考框折叠为"已完成思考"状态

---

### 标签7: `<final>最终富文本</final>`

**处理流程**:
```
XML解析 → SemanticEvent.FinalStart → ThinkingEvent.FinalStart
→ SemanticEvent.FinalToken → ThinkingEvent.FinalToken
→ SemanticEvent.FinalEnd → ThinkingEvent.FinalEnd
→ UI触发FinalRenderingReady → StreamingFinalRenderer打字机渲染
```

**传递链详细**:
```kotlin
// 1. 解析器检测<final>标签
StreamingThinkingMLParser.handleFinalStart()
→ emitEvent(SemanticEvent.FinalStart)

// 2. 流式Token处理
StreamingThinkingMLParser.handleToken()
→ emitEvent(SemanticEvent.FinalToken(content))

// 3. Domain映射
DomainMapper.mapSemanticToThinking(SemanticEvent.FinalToken)
→ ThinkingEvent.FinalToken(content)

// 4. Reducer状态更新
ThinkingReducer.reduce(ThinkingEvent.FinalToken)
→ state.copy(
    finalTokens = state.finalTokens + event.content,
    isFinalStreaming = true
)

// 5. UI渲染
StreamingFinalRenderer(
    finalTokens = uiState.finalTokens,
    isFinalStreaming = uiState.isFinalStreaming
) {
    // 打字机效果：逐个Token显示，delay(100)
    LaunchedEffect(finalTokens.size) {
        newTokens.forEach { token ->
            contentSegments = contentSegments + segment
            delay(100) // 🔥 打字机效果核心
        }
    }
}
```

---

## 🎛️ UI状态切换序列（v2.2双时序握手验证版）

### 完整用户体验流程 + 双时序握手验证

```
1. 用户发送消息
   ↓
2. ThinkingHeader显示"thinking"加载动画
   ↓
3. 【握手点1】Header → perthink
   ├─ 后台数据时序: <think> → TagOpened("think") → PhaseStart("perthink")
   ├─ 前台UI时序: Header渐隐 → perthink显示
   ├─ 状态切换: activePhaseId = "perthink"
   ├─ 标题: "Bro is thinking"（金属色字体）
   └─ 内容: 预思考内容实时刷新
   ↓
4. <thinking>检测 → 正式思考阶段准备
   ├─ 后台数据时序: <thinking> → 状态切换到THINKING
   └─ <phase>标签按序列处理
   ↓
5. 【握手点2】perthink → 正式phase
   ├─ 后台数据时序: </think> → PreThinkEnd → phase.isComplete = true
   ├─ 前台UI时序: perthink动画完成 → onAnimationFinished("perthink") → PhaseAnimFinished("perthink")
   ├─ 双握手检查: phase.isComplete && event.id == activePhaseId
   └─ 状态切换: activePhaseId = nextPhaseId
   ↓
6. Phase处理循环（双时序协调）
   ├─ 后台数据: <phase id="X"> → 创建/激活Phase
   ├─ 后台数据: <title> → 更新思考框标题
   ├─ 后台数据: 文本内容 → 逐字打印更新
   ├─ 后台数据: </phase> → 标记数据完成
   ├─ 前台UI: UI动画完成 → onAnimationFinished → PhaseAnimFinished事件
   └─ 双握手检查 → 切换下一Phase
   ↓
7. 【握手点3】正式phase → 思考完成
   ├─ 后台数据时序: </thinking> → ThinkingEnd → 标记数据结束
   ├─ 前台UI时序: 最后phase动画完成 → PhaseAnimFinished(lastPhaseId)
   ├─ 完成条件: nextPhaseId == null && phases.all { it.isComplete }
   └─ 状态切换: isThinkingComplete = true, activePhaseId = null
   ↓
8. SimpleSummaryText交互（可选）
   ├─ 显示: "已思考X时间，点击查看思考过程"
   ├─ 点击 → SummaryCard半屏弹出
   ├─ 拖拽条/手势 → 全屏模式切换
   └─ 思考过程完整展示
   ↓
9. 【握手点4】思考完成 → 富文本显示
   ├─ 后台数据时序: <final> → FinalStart → finalContentArrived = true
   ├─ 前台UI时序: 思考完成 + final内容到达 → FinalRenderingReady → finalRichTextReady = true
   ├─ 触发条件: isThinkingCompleted && finalContentArrived && !finalRichTextReady && !summaryTextVisible
   ├─ StreamingFinalRenderer激活
   ├─ FinalToken流式处理
   ├─ 打字机效果逐个显示(delay 100ms)
   ├─ 支持Mermaid图表、Markdown格式
   └─ 完成后显示复制按钮+Token计数
```

---

## 🏗️ UI层级架构

```kotlin
// features/coach/aicoach/ChatInterface.kt
ChatInterface {
    LazyColumn {  // ← 唯一纵向滚动容器
        // ... 历史对话

        // ThinkingBox完整实例 (key="thinking-${messageId}")
        ThinkingBoxInternal(
            uiState = thinkingUiState,
            messageId = messageId,
            onEventSend = { event -> viewModel.sendThinkingEvent(event) }
        )

        // ... 其他消息
    }
}

// features/thinkingbox/ThinkingBox.kt
ThinkingBoxInternal {
    Column {  // 🔥 纯Column布局，无嵌套滚动

        // Phase 0: ThinkingHeader（724方案简化版）
        AnimatedVisibility(visible = shouldShowThinkingHeader) {
            ThinkingHeader(
                title = if (hasPreThinking) uiState.preThinking else "thinking...",
                isStreaming = isCurrentlyStreaming,  // 🔥 724修复：简化参数
                hasContent = hasActualThinkingContent // 🔥 724修复：只判断是否有内容
            )
        }

        // Phase 1: AIThinkingCard (1/3屏幕高度约束)
        AnimatedVisibility(visible = hasActualThinkingContent) {
            AIThinkingCard {
                BoxWithConstraints { heightIn(max = configuration.screenHeight * 0.33f) }

                // 单卡实现：当前激活Phase的ThinkingStageCard（724方案修复版）
                ThinkingStageCard(
                    phase = currentPhase,
                    isPreThink = currentPhase.id == "perthink",
                    isActive = true,
                    // 🔥 724修复要点：
                    // - 标题：金属字体效果，粗体，字号比正文大1号
                    // - 内容：普通渲染，不使用金属动画
                    // - 移除：状态提示小点
                    onAnimationFinished = { phaseId ->
                        // 🔥 双握手关键：UI动画完成
                        onEventSend(ThinkingEvent.PhaseAnimFinished(phaseId))
                    }
                )
            }
        }

        // Phase 2: SimpleSummaryText
        AnimatedVisibility(visible = shouldShowSummaryText) {
            SimpleSummaryTextInternal(
                elapsed = uiState.elapsed,
                onClick = {
                    // 🔥 展开半屏SummaryCard
                    showSummaryPanel = true
                    onEventSend(ThinkingEvent.ShowSummaryPanel)
                }
            )
        }

        // Phase 3: StreamingFinalRenderer（724方案修复版）
        AnimatedVisibility(visible = shouldShowFinalRichText) {
            StreamingFinalRenderer(
                finalTokens = uiState.finalTokens,      // 🔥 Token流
                isFinalStreaming = uiState.isFinalStreaming,
                // 🔥 724修复：移除"正在接收内容"预设提示
                onRenderingComplete = {
                    val finalContent = uiState.finalMarkdown ?: uiState.finalTokens.joinToString("")
                    onMessageComplete?.invoke(messageId, finalContent)
                }
            )
        }

        // Phase 4: FinalActionsRow（724方案修复版）
        AnimatedVisibility(visible = shouldShowFinalActions) {
            FinalActionsRow(
                finalContent = finalMarkdown,
                tokenizerService = tokenizerService,
                // 🔥 724修复要点：
                // - 复制按钮：放置在左边
                // - Token计数：格式为"~tokens: 00"，斜体灰色偏浅
            )
        }

        // Phase 5: ScrollToBottomBtn（724方案修复版）
        ScrollToBottomBtn(
            visible = shouldShowScrollBtn,
            onClick = { /* 滚动到底部 */ },
            // 🔥 724修复：实现左右居中对齐（BottomCenter）
        )
    }

    // Summary面板 (覆盖层)
    if (showSummaryPanel) {
        SummaryCard(
            uiState = uiState,
            isExpanded = showSummaryPanel,
            onToggle = { showSummaryPanel = false }
        ) {
            // 半屏模式 + 拖拽条 + 全屏切换
            OptimizedThinkingBottomSheet(
                isFullScreen = isFullScreen,
                onToggleFullScreen = { isFullScreen = !isFullScreen },
                onDismiss = { showSummaryPanel = false }
            )
        }
    }
}
```

---

## 🔗 724方案修复总结 + v2.2双时序握手验证

### UI组件修复要点

1. **ThinkingStageCard** ✅
   - 标题：金属字体效果，粗体，字号比正文大1号（titleMedium）
   - 内容：普通渲染，不使用金属动画（ProgressiveTextRenderer）
   - 移除：状态提示小点（StatusDot）
   - 接口修复：使用正确的renderSpeed和fullText参数

2. **StreamingFinalRenderer** ✅
   - 移除："正在接收内容"预设提示
   - 保持：统一的流式打字机效果
   - 双时序：后台数据缓存 + 前台UI控制显示

3. **ScrollToBottomBtn** ✅
   - 布局：左右居中对齐（BottomCenter）

4. **FinalActionsRow** ✅
   - 复制按钮：放置在左边
   - Token计数：格式为`~tokens: 00`，斜体灰色偏浅

5. **ThinkingHeader** ✅
   - 简化逻辑：只判断思考框是否开始渲染
   - 移除：不必要的final和phase判断

### v2.2双时序架构修复要点

6. **perthink激活修复** ✅
   - 修复：<think>标签正确发送TagOpened事件
   - 修复：DomainMapper统一处理perthink创建
   - 修复：preThinking字段正确映射到UI状态

7. **双时序分离修复** ✅
   - 后台数据时序：所有标签处理只负责数据准备
   - 前台UI时序：onAnimationFinished作为唯一阶段转换控制器
   - 双握手机制：数据完成 + UI动画完成 = 真正切换

### 深度时序匹配修复

1. **PreThinkEnd时序切换** ✅
   ```kotlin
   // 检查是否有等待的正式phase需要激活
   val nextPhaseId = state.pending.firstOrNull()
   val shouldSwitchToNextPhase = nextPhaseId != null && state.activePhaseId == "perthink"

   if (shouldSwitchToNextPhase) {
       // 立即切换到下一个phase
       state.copy(activePhaseId = nextPhaseId, pending = newPending)
   }
   ```

2. **ThinkingEnd协调优化** ✅
   ```kotlin
   // 检查是否还有未完成的phase需要等待
   val hasIncompletePhases = state.phases.values.any { !it.isComplete }
   val hasActivePhasePending = state.activePhaseId != null

   if (hasIncompletePhases || hasActivePhasePending) {
       // 等待phase完成
   } else {
       // 直接设置思考完成
   }
   ```

3. **PhaseAnimFinished强制完成** ✅
   ```kotlin
   // 时序协调修复：检查是否需要强制设置思考完成
   val shouldForceComplete = nextPhaseId == null && !state.isStreaming
   val finalIsThinkingComplete = isThinkingComplete || shouldForceComplete
   ```

---

## 🚀 性能要求和技术规范（更新版）

### 1. 渲染性能
- **60fps流畅体验**: 所有动画保持60fps
- **30fps最低保证**: 低端设备不低于30fps
- **内存控制**: 单个ThinkingBox实例内存占用 < 50MB

### 2. 打字机效果参数
```kotlin
// StreamingFinalRenderer.kt - 724方案优化
LaunchedEffect(finalTokens.size) {
    newTokens.forEach { token ->
        contentSegments = contentSegments + parseToken(token)
        delay(33) // 🔥 优化：33ms间隔，每秒30个字符
    }
}

// PlainStreamingText.kt - 普通内容渲染
LaunchedEffect(fullText.length, phaseId) {
    for (i in displayedCharsCount until fullText.length) {
        displayedCharsCount = i + 1
        delay(if (isPreThink) 50L else 100L) // perthink更快
    }
}
```

### 3. 双时序架构超时
```kotlin
// ThinkingReducer.kt
val PHASE_TIMEOUT = 30_000L // 30秒超时保护
val THINKING_END_TIMEOUT = 10_000L // ThinkingEnd超时保护
```

---

## ⚠️ 重要约束和限制（724方案更新）

### 1. 单一实现原则
- **每个XML标签只能有一个处理逻辑** ✅
- **不允许重复实现相同功能** ✅
- **所有UI组件必须复用，不能重复创建** ✅

### 2. 时序严格性（724方案强化）
- **必须按XML标签顺序处理** ✅
- **Phase切换必须通过双握手机制** ✅
- **PreThinkEnd能够立即切换到等待phase** ✅
- **ThinkingEnd正确协调未完成phase** ✅

### 3. UI规范性（724方案要求）
- **标题使用金属字体粗体，内容普通渲染** ✅
- **复制按钮左置，token计数右置格式化** ✅
- **ScrollToBottomBtn居中对齐** ✅
- **移除不必要的状态提示和预设文本** ✅

---

**本文档为ThinkingBox模块开发的唯一权威标准，已完成724方案修复、深度时序优化和v2.2双时序握手验证。任何偏离都需要更新此文档并重新评审。**

---

## 📋 v2.2更新记录

### 2025-07-25 双时序握手验证完成
- ✅ 验证完整流程：Header → perthink → 正式phase → final → SimpleSummaryText + 富文本
- ✅ 修复perthink激活问题：确保<think>标签正确处理和状态映射
- ✅ 修复双时序接口：回归正确的参数命名（renderSpeed, fullText）
- ✅ 确认双时序架构核心原则：后台数据时序 + 前台UI时序分离
- ✅ 验证四个关键握手点的双握手机制正确实现
