package com.example.gymbro.core.logging

import android.annotation.SuppressLint
import android.util.Log
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【重构】增强的 Timber 日志管理器
 *
 * 统一管理Timber日志系统的初始化、配置和控制。
 * 集成模块级别的日志控制和性能优化。
 */
@Singleton
class TimberManager
    @Inject
    constructor(
        private val loggingConfig: LoggingConfig,
    ) {
        /**
         * 初始化日志系统
         *
         * @param isDebug 是否为调试模式
         * @param environment 环境类型
         */
        fun initialize(
            isDebug: Boolean,
            environment: LoggingConfig.Environment = LoggingConfig.Environment.DEVELOPMENT,
        ) {
            // 设置环境
            loggingConfig.setEnvironment(environment)

            // 清除所有已有的Tree
            Timber.uprootAll()

            // 根据环境安装适当的Tree
            when {
                isDebug -> {
                    // 开发环境：使用模块感知的Tree
                    Timber.plant(ModuleAwareTree(loggingConfig))
                    Timber.tag("LOG-MANAGER").i("🔥 开发环境日志系统已启动 - 模块感知模式")
                }

                environment == LoggingConfig.Environment.PRODUCTION -> {
                    // 生产环境：只记录错误
                    Timber.plant(ProductionTree())
                    Timber.tag("LOG-MANAGER").i("🔥 生产环境日志系统已启动 - 仅错误模式")
                }

                else -> {
                    // 测试环境：适度日志
                    Timber.plant(StagingTree(loggingConfig))
                    Timber.tag("LOG-MANAGER").i("🔥 测试环境日志系统已启动 - 适度日志模式")
                }
            }
        }

        /**
         * 🔥 快速配置：关闭 ThinkingBox 详细日志
         */
        fun disableThinkingBoxVerboseLogs() {
            loggingConfig.disableThinkingBoxVerboseLogs()
            Timber.tag("LOG-MANAGER").i("🔥 ThinkingBox 详细日志已关闭")
        }

        /**
         * 🔥 快速配置：启用 ThinkingBox 调试日志
         */
        fun enableThinkingBoxDebugLogs() {
            loggingConfig.enableThinkingBoxDebugLogs()
            Timber.tag("LOG-MANAGER").i("🔥 ThinkingBox 调试日志已启用")
        }

        /**
         * 🔥 快速配置：启用 TOKEN-FLOW 调试日志
         */
        fun enableTokenFlowDebugLogs() {
            loggingConfig.enableTokenFlowDebugLogs()
            Timber.tag("LOG-MANAGER").i("🔥 TOKEN-FLOW 调试日志已启用")
        }

        /**
         * 🔥 快速配置：关闭 TOKEN-FLOW 调试日志
         */
        fun disableTokenFlowDebugLogs() {
            loggingConfig.disableTokenFlowDebugLogs()
            Timber.tag("LOG-MANAGER").i("🔥 TOKEN-FLOW 调试日志已关闭")
        }

        /**
         * 🔥 快速配置：启用清洁日志模式
         * 关闭噪音日志，只保留核心调试信息
         */
        fun enableCleanLoggingMode() {
            loggingConfig.setEnvironment(LoggingConfig.Environment.DEVELOPMENT)
            // 只启用Template调试相关的模块
            loggingConfig.updateModuleConfig(
                LoggingConfig.MODULE_TEMPLATE_DEBUG,
                LoggingConfig.ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf("CRITICAL-SAVE", "CRITICAL-LOAD", "CRITICAL-DB", "USER-ID-DEBUG"),
                    sampleRate = 1,
                ),
            )
            Timber.tag("LOG-MANAGER").i("🔥 清洁日志模式已启用 - 只保留核心调试信息")
        }

        /**
         * 运行时切换环境
         */
        fun switchEnvironment(environment: LoggingConfig.Environment) {
            loggingConfig.setEnvironment(environment)
            Timber.tag("LOG-MANAGER").i("🔥 已切换到环境: $environment")
        }

        /**
         * 获取当前配置信息
         */
        fun getCurrentConfig(): String {
            val env = loggingConfig.getCurrentEnvironment()
        val tbConfig = loggingConfig.getModuleConfig(LoggingConfig.MODULE_THINKING_BOX)
        return "Environment: $env, ThinkingBox: ${tbConfig?.enabled}, MinLevel: ${tbConfig?.minLevel}"
    }

    /**
     * 在调试模式下打印日志堆栈，方便故障排查
     */
    @SuppressLint("TimberArgCount")
    fun dumpLogStack() {
        Timber.d(LOG_STACK_INDICATOR, "Current log stack:")
        val stackTrace = Thread.currentThread().stackTrace
        var startIndex = 0

        // 跳过一些内部框架调用
        for (i in stackTrace.indices) {
            if (stackTrace[i].className.contains("TimberManager")) {
                startIndex = i + 1
                break
            }
        }

        // 打印有用的堆栈信息
        for (i in startIndex until stackTrace.size) {
            val element = stackTrace[i]
            if (element.className.startsWith("dalvik.") ||
                element.className.startsWith("java.") ||
                element.className.startsWith("android.")
            ) {
                break
            }
            Timber.d(
                LOG_STACK_INDICATOR,
                "↳ %s.%s(%s:%d)",
                element.className,
                element.methodName,
                element.fileName,
                element.lineNumber,
            )
        }
    }

    /**
     * 设置全局日志标签过滤器
     *
     * @param tagFilter 标签过滤函数
     */
    fun setGlobalTagFilter(tagFilter: (String?) -> String) {
        globalTagFilter = tagFilter
    }

    companion object {
        private const val LOG_STACK_INDICATOR = "LogStack"

        // 全局标签过滤器
        @Volatile
        private var globalTagFilter: ((String?) -> String)? = null

        /**
         * 应用全局标签过滤器
         */
        internal fun applyTagFilter(
            tag: String?,
        ): String = globalTagFilter?.invoke(tag) ?: tag ?: "GymBro"

        /**
         * 基于当前类名生成的标签
         */
        @JvmStatic
        fun tagWithClassName(): String {
            val trace = Thread.currentThread().stackTrace
            var relevantIndex = 0

            // 找到调用处
            for (i in trace.indices) {
                val className = trace[i].className
                if (!className.contains("Logger") &&
                    !className.contains("Timber") &&
                    !className.contains("TimberManager")
                ) {
                    relevantIndex = i
                        break
                    }
                }

                // 从完整类名中提取简短类名
                val fullClassName = trace[relevantIndex].className
                return fullClassName.substring(fullClassName.lastIndexOf('.') + 1)
            }
        }
    }

/**
 * 🔥 【新增】生产环境专用 Tree
 * 只记录错误和崩溃信息
 */
class ProductionTree : Timber.Tree() {
    override fun isLoggable(
        tag: String?,
        priority: Int,
    ): Boolean {
        // 生产环境只记录错误和断言
        return priority >= Log.ERROR
    }

    override fun log(
        priority: Int,
        tag: String?,
        message: String,
        t: Throwable?,
    ) {
        if (!isLoggable(tag, priority)) return

        // 过滤敏感信息
        val sanitizedMessage = SensitiveDataFilter.filterSensitiveData(message)
        val finalTag = tag ?: "GymBro"

        // 记录到系统日志
        when (priority) {
            Log.ERROR -> Log.e(finalTag, sanitizedMessage, t)
            Log.ASSERT -> Log.wtf(finalTag, sanitizedMessage, t)
        }

        // 这里可以集成崩溃报告服务
        if (t != null) {
            reportCrash(finalTag, sanitizedMessage, t)
        }
    }

    private fun reportCrash(
        tag: String,
        message: String,
        throwable: Throwable,
    ) {
        // 集成 Firebase Crashlytics 或其他崩溃报告服务
        // 暂时留空，可以后续集成
    }
}

/**
 * 🔥 【新增】测试环境专用 Tree
 * 适度的日志输出，支持调试但不会刷屏
 */
class StagingTree(
    private val loggingConfig: LoggingConfig,
) : Timber.DebugTree() {
    override fun log(
        priority: Int,
        tag: String?,
        message: String,
        t: Throwable?,
    ) {
        // 测试环境：INFO 及以上 + 特定调试标签
        if (priority < Log.INFO) {
            // 只允许特定的调试标签
            if (tag == null || !isAllowedDebugTag(tag)) {
                return
            }
        }

        super.log(priority, tag, message, t)
    }

    private fun isAllowedDebugTag(tag: String): Boolean {
        return tag.contains("ERROR") ||
            tag.contains("STATE") ||
            tag.contains("UI") ||
            tag.contains("NETWORK") ||
            tag.contains("AUTH")
    }
}

/**
 * Timber日志标签助手
 *
 * 使用示例：
 * ```
 * Timber.tag(TimberTags.NETWORK).d("API request completed")
 * Timber.tag(TimberTags.DATABASE).i("Database updated")
 * ```
 */
object TimberTags {
    /** 网络相关日志 */
    const val NETWORK = "Network"

    /** 数据库相关日志 */
    const val DATABASE = "Database"

    /** 用户界面相关日志 */
    const val UI = "UI"

    /** 认证相关日志 */
    const val AUTH = "Auth"

    /** 同步相关日志 */
    const val SYNC = "Sync"

    /** 性能相关日志 */
    const val PERFORMANCE = "Performance"

    /** 业务逻辑相关日志 */
    const val BUSINESS = "Business"

    /** 系统相关日志 */
    const val SYSTEM = "System"
}
