package com.example.gymbro.core.logging

import android.util.Log
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【重构】统一日志配置管理器
 *
 * 功能：
 * - 模块级别的日志控制
 * - 运行时动态调整日志级别
 * - 生产环境和开发环境的不同策略
 * - 性能优化的日志过滤
 */
@Singleton
class LoggingConfig
    @Inject
    constructor() {
        /**
         * 模块日志配置
         */
        data class ModuleLogConfig(
            val enabled: Boolean = true,
            val minLevel: Int = Log.DEBUG,
            val tags: Set<String> = emptySet(),
            val sampleRate: Int = 1, // 采样率：1=全量，10=每10条记录1条
        )

        /**
         * 环境类型
         */
        enum class Environment {
            DEVELOPMENT,
            STAGING,
            PRODUCTION,
        }

        // 当前环境
        @Volatile
        private var currentEnvironment = Environment.DEVELOPMENT

        // 模块配置映射
        private val moduleConfigs = ConcurrentHashMap<String, ModuleLogConfig>()

        // 全局日志开关
        @Volatile
        private var globalEnabled = true

        init {
            setupDefaultConfigs()
        }

        /**
         * 设置默认配置
         */
        private fun setupDefaultConfigs() {
            // ThinkingBox 模块配置
            moduleConfigs[MODULE_THINKING_BOX] =
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG, // 🔥 启用DEBUG级别以查看详细日志
                    tags = setOf("TB-ERROR", "TB-STATE", "TB-UI", "ThinkingBox", "FINAL-DEBUG", "SimpleSummaryText", "TB-REDUCER", "状态映射完成"), // 🔥 添加状态映射调试标签
                    sampleRate = 1,
                )

            // Coach 模块配置
            moduleConfigs[MODULE_COACH] =
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf("COACH-AI", "COACH-UI", "COACH-ERROR"),
                    sampleRate = 1,
                )

            // Workout 模块配置
            moduleConfigs[MODULE_WORKOUT] =
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf("WORKOUT-SESSION", "WORKOUT-ERROR"),
                    sampleRate = 1,
                )

            // Core 模块配置
            moduleConfigs[MODULE_CORE] =
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.INFO,
                    tags = setOf("NETWORK", "DATABASE", "AUTH", "ERROR", "TOKEN-FLOW", "PHASE-DEBUG"), // 🔥 【711修复】添加PHASE-DEBUG支持
                    sampleRate = 1,
                )

            // 🔥 新增：Template 调试模块配置
            moduleConfigs[MODULE_TEMPLATE_DEBUG] =
                ModuleLogConfig(
                    enabled = TEMPLATE_WEIGHT_DEBUG_ENABLED,
                    minLevel = Log.DEBUG,
                    tags = setOf("TEMPLATE-WEIGHT-DEBUG", "CRITICAL-LOAD", "CRITICAL-DB", "CRITICAL-SAVE", "JSON-PARSE"),
                    sampleRate = 1,
                )
        }

        /**
         * 设置环境
         */
        fun setEnvironment(environment: Environment) {
            currentEnvironment = environment
            applyEnvironmentSettings()
        }

        /**
         * 应用环境设置
         */
        private fun applyEnvironmentSettings() {
            when (currentEnvironment) {
                Environment.DEVELOPMENT -> {
                    // 开发环境：适度的日志输出
                    updateModuleConfig(
                        MODULE_THINKING_BOX,
                        ModuleLogConfig(
                            enabled = true,
                            minLevel = Log.DEBUG, // 🔥 开发环境启用DEBUG级别
                            tags = setOf("TB-ERROR", "TB-STATE", "TB-UI", "ThinkingBox", "FINAL-DEBUG", "SimpleSummaryText", "TB-REDUCER", "状态映射完成"), // 🔥 包含所有调试标签
                            sampleRate = 1,
                        ),
                    )
                }

                Environment.STAGING -> {
                    // 测试环境：更多日志用于调试
                    updateModuleConfig(
                        MODULE_THINKING_BOX,
                        ModuleLogConfig(
                            enabled = true,
                            minLevel = Log.DEBUG,
                            tags = setOf("TB-ERROR", "TB-STATE", "TB-UI", "TB-MERMAID"),
                            sampleRate = 5, // 采样输出
                        ),
                    )
                }

                Environment.PRODUCTION -> {
                    // 生产环境：只记录错误和关键信息
                    moduleConfigs.forEach { (module, _) ->
                        updateModuleConfig(
                            module,
                            ModuleLogConfig(
                                enabled = true,
                                minLevel = Log.WARN,
                                tags = setOf("ERROR", "CRASH"),
                                sampleRate = 1,
                            ),
                        )
                    }
                }
            }
        }

        /**
         * 更新模块配置
         */
        fun updateModuleConfig(
            module: String,
            config: ModuleLogConfig,
        ) {
            moduleConfigs[module] = config
        }

        /**
         * 获取模块配置
         */
        fun getModuleConfig(module: String): ModuleLogConfig? = moduleConfigs[module]

        /**
         * 检查是否应该记录日志
         */
        fun shouldLog(
            module: String,
            tag: String?,
            priority: Int,
        ): Boolean {
            if (!globalEnabled) return false

            val config = moduleConfigs[module] ?: return false
            if (!config.enabled) return false
            if (priority < config.minLevel) return false

            // 检查标签过滤
            if (config.tags.isNotEmpty() && tag != null) {
                val shouldLogByTag =
                    config.tags.any { allowedTag ->
                        tag.contains(allowedTag, ignoreCase = true)
                    }
                if (!shouldLogByTag) return false
            }

            return true
        }

        /**
         * 全局开关控制
         */
        fun setGlobalEnabled(enabled: Boolean) {
            globalEnabled = enabled
        }

        /**
         * 获取当前环境
         */
        fun getCurrentEnvironment(): Environment = currentEnvironment

        /**
         * 🔥 ThinkingBox 专用：关闭详细日志
         */
        fun disableThinkingBoxVerboseLogs() {
            updateModuleConfig(
                MODULE_THINKING_BOX,
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.WARN, // 只显示警告和错误
                    tags = setOf("TB-ERROR"), // 只保留错误标签
                    sampleRate = 1,
                ),
            )
        }

        /**
         * 🔥 ThinkingBox 专用：启用调试模式
         */
        fun enableThinkingBoxDebugLogs() {
            updateModuleConfig(
                MODULE_THINKING_BOX,
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf(
                        "TB-ERROR", "TB-STATE", "TB-UI", "TB-CONTENT", "PHASE-DEBUG", "TB-MERMAID", "TB-FINAL",
                        // 🔥 【Phase内容显示修复】添加新的调试标签
                        "TB-PARSER", "TB-MAPPER", "TB-REDUCER", "TB-ANIM",
                        // 🔥 【SimpleSummaryText调试】添加关键调试标签
                        "ThinkingBox", "FINAL-DEBUG", "SimpleSummaryText", "TB-REDUCER", "状态映射完成"
                    ),
                    sampleRate = 1, // 🔥 【711修复】全量输出，用于深度调试
                ),
            )
        }

        /**
         * 🔥 TOKEN-FLOW 专用：启用流式响应调试模式
         */
        fun enableTokenFlowDebugLogs() {
            // Core 模块：启用 TOKEN-FLOW 调试
            updateModuleConfig(
                MODULE_CORE,
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf("NETWORK", "DATABASE", "AUTH", "ERROR", "TOKEN-FLOW"),
                    sampleRate = 1, // 全量输出，用于调试流式响应
                ),
            )

            // ThinkingBox 模块：启用相关调试
            updateModuleConfig(
                MODULE_THINKING_BOX,
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf("TB-ERROR", "TB-STATE", "TB-UI", "TB-RAW", "TOKEN-FLOW"),
                    sampleRate = 1, // 全量输出
                ),
            )

            // Coach 模块：启用AI相关调试
            updateModuleConfig(
                MODULE_COACH,
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf("COACH-AI", "COACH-UI", "COACH-ERROR", "TOKEN-FLOW"),
                    sampleRate = 1,
                ),
            )
        }

        /**
         * 🔥 TOKEN-FLOW 专用：关闭流式响应调试模式
         */
        fun disableTokenFlowDebugLogs() {
            // 恢复默认配置
            setupDefaultConfigs()
        }

        /**
         * 🔥 TOKEN-FLOW 专用：静音模式 - 只显示TOKEN-FLOW和错误日志
         */
        fun enableTokenFlowOnlyMode() {
            // Core 模块：只显示 TOKEN-FLOW 和错误
            updateModuleConfig(
                MODULE_CORE,
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf("TOKEN-FLOW", "ERROR"),
                    sampleRate = 1,
                ),
            )

            // Coach 模块：只显示 TOKEN-FLOW 和错误
            updateModuleConfig(
                MODULE_COACH,
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf("TOKEN-FLOW", "COACH-ERROR"),
                    sampleRate = 1,
                ),
            )

            // ThinkingBox 模块：只显示错误
            updateModuleConfig(
                MODULE_THINKING_BOX,
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.ERROR,
                    tags = setOf("TB-ERROR"),
                    sampleRate = 1,
                ),
            )

            // Workout 模块：只显示错误
            updateModuleConfig(
                MODULE_WORKOUT,
                ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.ERROR,
                    tags = setOf("WORKOUT-ERROR"),
                    sampleRate = 1,
                ),
            )
        }

        companion object {
            // 模块常量
            const val MODULE_THINKING_BOX = "ThinkingBox"
            const val MODULE_COACH = "Coach"
            const val MODULE_WORKOUT = "Workout"
            const val MODULE_CORE = "Core"
            const val MODULE_DESIGN_SYSTEM = "DesignSystem"
            const val MODULE_TEMPLATE_DEBUG = "TemplateDebug" // 🔥 新增：专门用于 Template 调试

            // 🔥 新增：噪音日志控制开关
            /**
             * MVI 架构日志开关 - 控制 BaseMviViewModel 等噪音日志
             */
            @JvmStatic
            var MVI_LOGGING_ENABLED = false

            /**
             * System.out 日志开关 - 控制 System.out.println 噪音日志
             * 🔥 清理：关闭大部分System.out噪音，只保留CRITICAL标签
             */
            @JvmStatic
            var SYSTEM_OUT_LOGGING_ENABLED = false

            /**
             * Template 重量数据调试日志开关 - 专门用于调试重量重置问题
             * 🔥 保留：Template保存调试仍然需要
             */
            @JvmStatic
            var TEMPLATE_WEIGHT_DEBUG_ENABLED = true

            /**
             * 关键数据流日志开关 - CRITICAL-LOAD, CRITICAL-DB 等
             * 🔥 保留：关键数据流调试
             */
            @JvmStatic
            var CRITICAL_DATA_LOGGING_ENABLED = true

            // 标签常量
            object Tags {
                // ThinkingBox 标签
                const val TB_ERROR = "TB-ERROR"
                const val TB_STATE = "TB-STATE"
                const val TB_UI = "TB-UI"
                const val TB_MERMAID = "TB-MERMAID"
                const val TB_FINAL = "TB-FINAL"
                const val TB_RAW = "TB-RAW"

                // Coach 标签
                const val COACH_AI = "COACH-AI"
                const val COACH_UI = "COACH-UI"
                const val COACH_ERROR = "COACH-ERROR"

                // Core 标签
                const val NETWORK = "NETWORK"
                const val DATABASE = "DATABASE"
                const val AUTH = "AUTH"
                const val ERROR = "ERROR"

                // 🔥 新增：Template 调试专用标签
                const val TEMPLATE_WEIGHT_DEBUG = "TEMPLATE-WEIGHT-DEBUG"
                const val CRITICAL_LOAD = "CRITICAL-LOAD"
                const val CRITICAL_DB = "CRITICAL-DB"
                const val CRITICAL_SAVE = "CRITICAL-SAVE"
                const val JSON_PARSE = "JSON-PARSE"

                // 🔥 新增：噪音日志标签（用于过滤）
                const val MVI_DEBUG = "MVI-DEBUG"
                const val SYSTEM_OUT = "SYSTEM-OUT"
            }
        }
    }

/**
 * 🔥 【重构】模块感知的 Timber Tree
 *
 * 根据模块配置自动过滤日志
 */
class ModuleAwareTree(
    private val loggingConfig: LoggingConfig,
) : Timber.DebugTree() {
    private val sampleCounters = ConcurrentHashMap<String, Int>()

    override fun log(
        priority: Int,
        tag: String?,
        message: String,
        t: Throwable?,
    ) {
        // 🔥 新增：噪音日志过滤
        if (shouldFilterNoiseLog(tag, message)) {
            return
        }

        val module = determineModule(tag)

        // 检查是否应该记录
        if (!loggingConfig.shouldLog(module, tag, priority)) {
            return
        }

        // 采样控制
        val config = loggingConfig.getModuleConfig(module)
        if (config != null && config.sampleRate > 1) {
            val key = "$module-$tag"
            val count = sampleCounters.merge(key, 1) { old, _ -> old + 1 } ?: 1
            if (count % config.sampleRate != 0) {
                return
            }
        }

        super.log(priority, tag, message, t)
    }

    /**
     * 🔥 新增：噪音日志过滤逻辑
     */
    private fun shouldFilterNoiseLog(tag: String?, message: String): Boolean {
        // 过滤 MVI 架构噪音日志
        if (!LoggingConfig.MVI_LOGGING_ENABLED) {
            if (tag == "System.out" && (
                message.contains("[DEBUG] BaseMviViewModel") ||
                message.contains("🎯 [DEBUG] BaseMviViewModel") ||
                message.contains("🔧 [DEBUG] BaseMviViewModel") ||
                message.contains("🔄 [DEBUG] BaseMviViewModel") ||
                message.contains("✨ [DEBUG] BaseMviViewModel") ||
                message.contains("🔍 [DEBUG] isAllCoreDataLoaded") ||
                message.contains("[SMART-LOADING]") ||
                message.contains("HomeViewModel") ||
                message.contains("TemplateViewModel") ||
                message.contains("🔧 [DEBUG] onNavigateToNewTemplate") ||
                message.contains("🔧 [DEBUG] 导航到 CREATE_TEMPLATE") ||
                message.contains("🚀 [DEBUG] TopBar返回按钮被点击") ||
                message.contains("🎬 [DEBUG] MainScreen") ||
                message.contains("NavigateToTemplates") ||
                message.contains("🔧 [DEBUG] TemplateViewModel") ||
                message.contains("Effect 已传递给 EffectHandler") ||
                message.contains("EffectHandler返回Intent") ||
                message.contains("处理Effect:") ||
                message.contains("收到 Effect:")
            )) {
                return true
            }
        }

        // 过滤 System.out 噪音日志，但保留CRITICAL标签
        if (!LoggingConfig.SYSTEM_OUT_LOGGING_ENABLED) {
            if (tag == "System.out" && 
                !message.contains("CRITICAL-") && 
                !message.contains("TEMPLATE-WEIGHT-DEBUG") &&
                !message.contains("USER-ID-DEBUG")) {
                return true
            }
        }

        return false
    }

    /**
     * 根据标签确定模块
     */
    private fun determineModule(tag: String?): String =
        when {
            tag?.startsWith("TB-") == true -> LoggingConfig.MODULE_THINKING_BOX
            tag?.startsWith("PHASE-DEBUG") == true -> LoggingConfig.MODULE_THINKING_BOX // 🔥 【711修复】PHASE-DEBUG归类到ThinkingBox
            tag?.contains("ThinkingBox") == true -> LoggingConfig.MODULE_THINKING_BOX // 🔥 【SimpleSummaryText调试】ThinkingBox标签归类
            tag?.contains("FINAL-DEBUG") == true -> LoggingConfig.MODULE_THINKING_BOX // 🔥 【SimpleSummaryText调试】FINAL-DEBUG标签归类
            tag?.contains("SimpleSummaryText") == true -> LoggingConfig.MODULE_THINKING_BOX // 🔥 【SimpleSummaryText调试】专用标签归类
            tag?.contains("TB-REDUCER") == true -> LoggingConfig.MODULE_THINKING_BOX // 🔥 【Reducer调试】TB-REDUCER标签归类
            tag?.contains("状态映射完成") == true -> LoggingConfig.MODULE_THINKING_BOX // 🔥 【状态映射调试】状态映射标签归类
            tag?.startsWith("COACH-") == true -> LoggingConfig.MODULE_COACH
            tag?.startsWith("WORKOUT-") == true -> LoggingConfig.MODULE_WORKOUT
            // 🔥 新增：Template 调试标签归类
            tag?.startsWith("TEMPLATE-WEIGHT-DEBUG") == true -> LoggingConfig.MODULE_TEMPLATE_DEBUG
            tag?.startsWith("CRITICAL-") == true -> LoggingConfig.MODULE_TEMPLATE_DEBUG
            tag?.startsWith("JSON-PARSE") == true -> LoggingConfig.MODULE_TEMPLATE_DEBUG
            tag?.contains("NETWORK") == true -> LoggingConfig.MODULE_CORE
            tag?.contains("DATABASE") == true -> LoggingConfig.MODULE_CORE
            tag?.contains("AUTH") == true -> LoggingConfig.MODULE_CORE
            else -> LoggingConfig.MODULE_CORE
        }
}
