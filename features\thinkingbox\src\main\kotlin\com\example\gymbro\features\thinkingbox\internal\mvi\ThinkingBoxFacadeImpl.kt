package com.example.gymbro.features.thinkingbox.internal.mvi

import com.example.gymbro.features.thinkingbox.domain.interfaces.ThinkingBoxFacade
import com.example.gymbro.features.thinkingbox.domain.interfaces.UiState
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ThinkingBoxFacade 实现类 - 多轮对话状态隔离版本
 *
 * 🔥 v9.0 多轮对话状态隔离架构：
 * - 使用 ThinkingBoxManager 管理多个实例
 * - 每个 messageId 对应独立的状态
 * - 支持并发处理多个对话
 * - 完全隔离，不会相互干扰
 *
 * 设计原则：
 * - 代理模式：将实际工作委托给 ThinkingBoxInstance
 * - 状态隔离：每个对话有独立的状态管理
 * - 资源管理：支持实例生命周期管理
 * - 向后兼容：保持接口不变
 */
@Singleton
class ThinkingBoxFacadeImpl
@Inject
constructor(
    private val manager: ThinkingBoxManager,
    private val tokenBus: com.example.gymbro.core.network.eventbus.TokenBus, // 🔥 【架构重构阶段三】注入全局TokenBus
    private val tokenRouter: com.example.gymbro.core.network.router.TokenRouter, // 🔥 注入TokenRouter用于路由
) : ThinkingBoxFacade {
    companion object {
        private const val TAG = "ThinkingBoxFacade"
    }

    override suspend fun start(): Flow<UiState> {
        Timber.tag(TAG).w("🚨 使用了废弃的 start() 方法，建议使用 startWithMessageId()")
        // 🔥 为向后兼容，生成临时 messageId
        val tempMessageId = "temp-${System.currentTimeMillis()}"
        return startWithMessageId(tempMessageId)
    }

    override suspend fun startWithMessageId(messageId: String): Flow<UiState> {
        Timber.tag(TAG).e("🚨 [调试] startWithMessageId被调用: messageId=$messageId")
        println("🚨 [调试] startWithMessageId被调用: messageId=$messageId")

        Timber.tag(TAG).i("🔥 [多轮对话] 启动ThinkingBox: messageId=$messageId")

        // 🔥 获取或创建对应的实例（暂时不传递回调，等待Coach模块集成）
        val instance = manager.getOrCreateInstance(messageId)

        Timber.tag(TAG).e("🚨 [调试] ThinkingBoxInstance已创建: messageId=$messageId, instance=${instance.hashCode()}")
        println("🚨 [调试] ThinkingBoxInstance已创建: messageId=$messageId, instance=${instance.hashCode()}")

        // 🔥 启动实例并返回状态流
        val flow = instance.start()

        Timber.tag(TAG).e("🚨 [调试] ThinkingBoxInstance已启动: messageId=$messageId")
        println("🚨 [调试] ThinkingBoxInstance已启动: messageId=$messageId")

        return flow
    }

    /**
     * 🔥 【思考内容保存修复】带回调的启动方法
     */
    override suspend fun startWithMessageId(
        messageId: String,
        onAiMessageComplete: (
            (
                messageId: String,
                finalMarkdown: String,
                thinkingNodes: String?,
            ) -> Unit
        )?,
    ): Flow<UiState> {
        Timber.tag(TAG).i("🔥 [思考内容保存] 启动ThinkingBox with callback: messageId=$messageId")

        // 🔥 获取或创建对应的实例，传递回调
        val instance = manager.getOrCreateInstance(messageId, onAiMessageComplete)

        // 🔥 启动实例并返回状态流
        return instance.start()
    }

    override fun sendEvent(event: ThinkingEvent) {
        // V1事件系统已移除，保留空实现以维持接口兼容性
    }

    override fun sendEventToInstance(messageId: String, event: ThinkingEvent): Boolean {
        // 🔥 【问题2修复】发送事件到特定实例，用于UI动画完成回调
        return try {
            val instance = manager.getInstance(messageId)
            if (instance != null) {
                instance.sendEvent(event)
                Timber.tag(
                    TAG,
                ).d("🎯 [动画协调] 发送事件到实例: messageId=$messageId, event=${event::class.simpleName}")
                true
            } else {
                Timber.tag(TAG).w("⚠️ [动画协调] 实例不存在: messageId=$messageId")
                false
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ [动画协调] 发送事件失败: messageId=$messageId")
            false
        }
    }

    override fun submit(token: String): Boolean {
        return true
    }

    override suspend fun reset() {
        manager.clearAllInstances()
    }

    override fun markPersisted() {
        // 全局操作已不支持，需要指定messageId
    }

    override val currentState: UiState
        get() = UiState()

    /**
     * 🔥 【架构重构阶段三】实现事件总线方法
     * 从全局 TokenBus 订阅事件，实现完全解耦的架构
     */
    override suspend fun startWithEventBus(
        messageId: String,
        onAiMessageComplete: ((messageId: String, finalMarkdown: String, thinkingNodes: String?) -> Unit)?,
    ): kotlinx.coroutines.flow.Flow<UiState> {
        Timber.tag(TAG).d("🔄 [事件总线] 启动事件总线连接: messageId=$messageId")

        // 获取或创建 ThinkingBox 实例
        val instance = manager.getOrCreateInstance(messageId, onAiMessageComplete)

        // 启动实例的 UiState 流
        val uiStateFlow = instance.start()

        // 🔥 【事件总线架构】从 TokenBus 订阅事件并路由到 ConversationScope
        kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
            tokenBus.subscribe(messageId).collect { tokenEvent ->
                Timber.tag(
                    TAG,
                ).d(
                    "🔄 [事件总线] 处理token: messageId=${tokenEvent.messageId}, length=${tokenEvent.token.length}",
                )
                // 🔥 通过 TokenRouter 路由 token 到 ConversationScope
                try {
                    tokenRouter.routeToken(messageId, tokenEvent.token)
                } catch (e: Exception) {
                    Timber.tag(TAG).e(e, "❌ [事件总线] Token路由失败: messageId=$messageId")
                }
            }
        }

        return uiStateFlow
    }

    /**
     * 🔥 【向后兼容】保留直接连接方法
     */
    override suspend fun startWithDirectConnection(
        messageId: String,
        tokenEventFlow: kotlinx.coroutines.flow.Flow<com.example.gymbro.core.network.eventbus.TokenEvent>,
        onAiMessageComplete: ((messageId: String, finalMarkdown: String, thinkingNodes: String?) -> Unit)?,
    ): kotlinx.coroutines.flow.Flow<UiState> {
        Timber.tag(TAG).d("🔄 [直接连接] 启动直接连接: messageId=$messageId")

        // 获取或创建 ThinkingBox 实例
        val instance = manager.getOrCreateInstance(messageId, onAiMessageComplete)

        // 启动实例的 UiState 流
        val uiStateFlow = instance.start()

        // 在后台协程中处理 TokenEvent 流
        kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
            tokenEventFlow
                .filter { it.messageId == messageId } // 过滤只属于当前消息的 token
                .collect { tokenEvent ->
                    Timber.tag(
                        TAG,
                    ).d(
                        "🔄 [直接连接] 处理token: messageId=${tokenEvent.messageId}, length=${tokenEvent.token.length}",
                    )
                    // 🔥 通过 TokenRouter 路由 token 到 ConversationScope
                    try {
                        tokenRouter.routeToken(messageId, tokenEvent.token)
                    } catch (e: Exception) {
                        Timber.tag(TAG).e(e, "❌ [直接连接] Token路由失败: messageId=$messageId")
                    }
                }
        }

        return uiStateFlow
    }

    /**
     * 🔥 新增：获取管理器实例（用于调试和高级操作）
     */
    fun getManager(): ThinkingBoxManager = manager
}
