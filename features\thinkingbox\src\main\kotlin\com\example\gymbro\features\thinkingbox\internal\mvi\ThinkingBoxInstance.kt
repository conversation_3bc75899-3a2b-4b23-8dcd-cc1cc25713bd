package com.example.gymbro.features.thinkingbox.internal.mvi

import com.example.gymbro.features.thinkingbox.domain.interfaces.UiState
import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.domain.reducer.ThinkingReducer
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.time.Duration
import kotlin.time.Duration.Companion.milliseconds

/**
 * ThinkingBoxInstance - 独立的 ThinkingBox 实例
 *
 * 每个messageId对应一个独立实例，支持并发处理多个对话
 */
class ThinkingBoxInstance(
    private val messageId: String,
    // 🔥 【多轮对话架构升级】使用ConversationScope替代coroutineScope
    private val conversationScope: com.example.gymbro.core.network.router.ConversationScope,
    // 🔥 【RAW BUS清理】移除 domainMapper 依赖，使用无状态 DomainMapperStateless
    private val historySaver: com.example.gymbro.features.thinkingbox.internal.history.HistorySaver,
    // 🔥 【中优先级修复2】注入TokenizerService用于token计算
    private val tokenizerService: com.example.gymbro.core.ai.tokenizer.TokenizerService,
    // 🔥 【思考内容保存修复】AI消息完成回调
    private val onAiMessageComplete: (
        (messageId: String, finalMarkdown: String, thinkingNodes: String?) -> Unit
    )? = null,
) {
    companion object {
        private const val TAG = "ThinkingBoxInstance"
    }

    // 🔥 【V2系统提升】独立状态管理 - 每个实例有自己的V2状态
    private val isStarted = AtomicBoolean(false)
    private var parserJob: Job? = null
    private var finalRenderingTimeoutJob: Job? = null

    // 🔥 【Token收集器】统一RAW TOKEN收集和记录
    private val tokenCollector = StringBuilder()
    private var tokenCount = 0
    private val TOKEN_LOG_INTERVAL = 100 // 每100个token记录一次

    // 🔥 【V2→V1提升完成】映射器和解析器（使用统一的V1组件）
    private val domainMapper = DomainMapper()
    private val streamingParser = StreamingThinkingMLParser(
        xmlScanner = com.example.gymbro.features.thinkingbox.domain.parser.XmlStreamScanner(),
        functionCallDetector = com.example.gymbro.features.thinkingbox.domain.parser.FunctionCallDetector(),
    )
    // 🔥 【perthink修复】添加RawChunkProcessor用于XML标签清洗
    private val rawChunkProcessor = com.example.gymbro.features.thinkingbox.domain.processor.RawChunkProcessor()
    // 🔥 【状态管理】恢复MappingContext用于状态跟踪
    private var mappingContext = DomainMapper.MappingContext()
    private val _uiState = MutableStateFlow(ThinkingReducer.ThinkingUiState())

    // 🔥 实例创建时间，用于清理策略
    private val createdAt = System.currentTimeMillis()

    // 🔥 【系统统一】事件流 - 独立的事件处理
    private val thinkingEventFlow = kotlinx.coroutines.flow.MutableSharedFlow<ThinkingEvent>()

    // 🔥 【Token流修复】订阅就绪信号
    private var subscriptionReady = kotlinx.coroutines.CompletableDeferred<Unit>()

    init {
        Timber.tag(TAG).d("🔥 创建ThinkingBox实例: messageId=$messageId")

        // 🔥 【V2系统提升】TODO: 需要更新HistorySaver以支持V2事件流
        // historySaver.registerInstanceEventFlow(thinkingEventFlow, messageId)
    }

    /**
     * 🔥 【统一Token收集】收集RAW TOKEN并每100个token记录一次
     */
    private fun collectAndLogToken(rawToken: String) {
        // 收集token
        tokenCollector.append(rawToken)
        tokenCount++

        // 每100个token记录一次完整内容
        if (tokenCount % TOKEN_LOG_INTERVAL == 0) {
            val collectedContent = tokenCollector.toString()
            Timber.tag("RAW-TOKEN-COLLECTOR").i(
                "🔥 [Token收集] messageId=$messageId, 已收集${tokenCount}个token, " +
                "总长度=${collectedContent.length}字符\n" +
                "完整内容:\n$collectedContent"
            )
        }

        // 检查关键标签
        if (rawToken.contains("<think>") || rawToken.contains("</think>")) {
            Timber.tag("RAW-TOKEN-COLLECTOR").e(
                "🚨 [关键标签检测] messageId=$messageId, token#$tokenCount 包含think标签: '$rawToken'"
            )
        }
    }

    /**
     * 启动此实例的处理流程
     * 🔥 【多轮对话架构升级】使用ConversationScope和parseTokenStream函数
     * 🔥 【Token流修复】确保订阅建立后再返回Flow
     */
    suspend fun start(): Flow<UiState> {
        Timber.tag(TAG).i("🔥 启动ThinkingBox实例: messageId=$messageId, isStarted=${isStarted.get()}")

        // 🔥 【关键修复】每次start都重置状态，确保不会使用旧数据
        // 即使实例已存在，也要清理旧的phases等状态数据
        resetInternalState()

        if (!isStarted.compareAndSet(false, true)) {
            Timber.tag(TAG).i("🔥 实例重启: messageId=$messageId, 重置状态并继续")
            // 🔥 即使实例已启动，也要重置isStarted以允许重新启动
            isStarted.set(false)
            if (!isStarted.compareAndSet(false, true)) {
                Timber.tag(TAG).w("🚨 实例启动竞争条件，返回现有流")
                return _uiState.map { convertToStandardUiState(it) }
            }
        }

        // 🔥 取消之前的Parser协程，防止重复监听
        parserJob?.cancel()

        // 🔥 【Token流修复】重置订阅就绪信号
        if (subscriptionReady.isCompleted) {
            // 如果之前已完成，创建新的CompletableDeferred
            subscriptionReady = kotlinx.coroutines.CompletableDeferred<Unit>()
            Timber.tag(TAG).d("🔥 订阅信号已完成，创建新的CompletableDeferred")
        }

        // 🔥 【Token流修复】启动主处理协程，确保token流正确处理
        parserJob =
            conversationScope.launch {
                try {
                    Timber.tag(TAG).i("🔥 启动ConversationScope处理协程: messageId=$messageId")
                    Timber
                        .tag("TOKEN-FLOW")
                        .i("🎬 [ThinkingBoxInstance] 启动token流处理: messageId=$messageId")

                    // 🔥 【延迟优化】立即发出就绪信号，不等待订阅建立，减少延迟
                    subscriptionReady.complete(Unit)
                    Timber.tag("TOKEN-FLOW").i("✅ [ThinkingBoxInstance] 立即发出就绪信号，减少延迟: messageId=$messageId")

                    // 🔥 【延迟优化】立即设置初始思考状态，让UI立即显示思考框
                    _uiState.value = _uiState.value.copy(
                        isStreaming = true,
                        version = _uiState.value.version + 1,
                    )
                    Timber.tag("TOKEN-FLOW").i("🎬 [ThinkingBoxInstance] 立即激活思考状态: messageId=$messageId")

                    // 🔥 【关键修复】异步等待订阅建立，不阻塞主流程
                    launch {
                        val subscriptionEstablished = conversationScope.waitForTokenSubscription(2000) // 减少超时时间
                        if (!subscriptionEstablished) {
                            Timber
                                .tag("TOKEN-FLOW")
                                .w("⚠️ [ThinkingBoxInstance] Token订阅建立超时，但继续处理: messageId=$messageId")
                        } else {
                            Timber.tag("TOKEN-FLOW").d("✅ [ThinkingBoxInstance] Token订阅建立成功: messageId=$messageId")
                        }
                    }

                    // 🔥 启动解析器，监听 conversationScope.tokens
                    val parserJob =
                        launch(kotlinx.coroutines.CoroutineName("Parser-$messageId")) {
                            Timber
                                .tag("TOKEN-FLOW")
                                .i("🎧 [ThinkingBoxInstance] 开始监听token流: messageId=$messageId")

                            try {
                                // 🔥 【V2→V1提升完成】使用统一的StreamingThinkingMLParser
                                // Timber.tag("PHASE-DEBUG").e("🚀 [ThinkingBoxInstance] 即将调用解析器: messageId=$messageId")

                                // 🔥 【唯一措施】token流开始赋予phase id="perthink"，通过<thinking>关闭
                                Timber.tag("RAW-TOKEN-COLLECTOR").e("🔥 [唯一措施] token流开始创建perthink phase，通过<thinking>关闭")

                                // 创建初始映射上下文，并设置为perthink状态
                                var localMappingContext = DomainMapper.MappingContext(
                                    currentPhaseId = "perthink",
                                    hasPerthinkStarted = true
                                )

                                val perthinkEvent = com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent.PhaseStart(
                                    "perthink",
                                    com.example.gymbro.features.thinkingbox.internal.constants.ThinkingBoxStrings.PERTHINK_TITLE
                                )

                                // 立即发送perthink事件，确保UI立即显示
                                val mappingResult = domainMapper.mapSemanticToThinking(perthinkEvent, localMappingContext)
                                mappingResult.events.forEach { thinkingEvent ->
                                    Timber.tag("RAW-TOKEN-COLLECTOR").e("🚨 [保险措施] 发送perthink事件: ${thinkingEvent::class.simpleName}")
                                    thinkingEventFlow.tryEmit(thinkingEvent)
                                }

                                // 更新映射上下文
                                localMappingContext = mappingResult.context

                                // 🔥 【perthink修复】对token流进行预处理，清洗<phase:PLAN>等XML标签
                                val preprocessedTokens = conversationScope.tokens.map { rawToken ->
                                    // 🔥 【统一Token收集】收集所有RAW TOKEN并定期记录
                                    collectAndLogToken(rawToken)

                                    val cleanedToken = rawChunkProcessor.preprocess(rawToken)
                                    cleanedToken
                                }

                                streamingParser.parseTokenStream(
                                    messageId = messageId,
                                    tokens = preprocessedTokens,
                                    onEvent = { event ->
                                        // 🔥 【调试日志】记录事件处理（已关闭噪音）
                                        // Timber.tag("TOKEN-FLOW").d("📨 [ThinkingBoxInstance] 收到SemanticEvent: messageId=$messageId, event=${event::class.simpleName}")
                                        // Timber.tag("PHASE-DEBUG").e("📨 [ThinkingBoxInstance] 收到事件: ${event::class.simpleName}")

                                        // 🔥 706任务保存：检查FinalArrived事件，触发历史保存
                                        if (event is SemanticEvent.FinalArrived) {
                                            // 🔥 【最终富文本调试】添加FinalArrived事件调试日志
                                            Timber.tag("TB-INSTANCE").d("🎯 [FinalArrived事件] 收到FinalArrived: messageId=$messageId, markdown长度=${event.markdown.length}")
                                            Timber.tag("TB-INSTANCE").d("🎯 [FinalArrived事件] markdown内容预览: '${event.markdown.take(100)}...'")
                                            handleFinalArrivedEvent(event)
                                        }

                                        // 🔥 【关键修复】发射SemanticEvent到ConversationScope，让DomainMapper处理映射
                                        conversationScope.launch {
                                            try {
                                                // 🔥 【事件发射调试】添加所有事件的发射日志
                                                Timber.tag("RAW-TOKEN-COLLECTOR").e("🔥 [事件发射] 即将发射事件: ${event::class.simpleName} - $event")

                                                conversationScope.emitEvent(event)

                                                Timber.tag("RAW-TOKEN-COLLECTOR").e("🔥 [事件发射] 事件发射成功: ${event::class.simpleName}")
                                            } catch (e: Exception) {
                                                Timber.tag("RAW-TOKEN-COLLECTOR").e("🔥 [事件发射] 事件发射失败: ${event::class.simpleName} - ${e.message}")
                                            }
                                        }
                                    },
                                )

                                // 🔥 【V2系统统一】V2解析器完成
                                Timber.tag("PHASE-DEBUG").e("✅ [ThinkingBoxInstance] V2解析器完成: messageId=$messageId")
                            } catch (e: Exception) {
                                Timber
                                    .tag("TOKEN-FLOW")
                                    .e(e, "❌ [ThinkingBoxInstance] V2解析器异常: messageId=$messageId")

                                // 🔥 【V2系统统一】V2解析器异常
                                Timber.tag("PHASE-DEBUG").e(e, "❌ [ThinkingBoxInstance] V2解析器异常: messageId=$messageId")
                                throw e
                            }
                        }

                    // 🔥 【关键修复】启动 DomainMapper，监听 conversationScope.events
                    val mapperJob =
                        conversationScope.launch(kotlinx.coroutines.CoroutineName("Mapper-$messageId")) {
                            Timber
                                .tag("TOKEN-FLOW")
                                .i("🗺️ [ThinkingBoxInstance] 开始监听事件流: messageId=$messageId")

                            try {
                                conversationScope.events.collect { event ->
                                    // 🔥 【事件监听调试】添加事件接收日志
                                    Timber.tag("RAW-TOKEN-COLLECTOR").e("🔥 [事件监听] 收到事件: ${event::class.simpleName} - $event")

                                    // 🔥 类型安全转换：确保事件是SemanticEvent类型
                                    if (event is SemanticEvent) {
                                        Timber.tag("RAW-TOKEN-COLLECTOR").e("🔥 [事件监听] 确认为SemanticEvent: ${event::class.simpleName}")
                                        Timber
                                            .tag("TOKEN-FLOW")
                                            .d("🗺️ [ThinkingBoxInstance] 处理SemanticEvent: messageId=$messageId, event=${event::class.simpleName}")
                                        // println("🗺️ [ThinkingBoxInstance] 处理SemanticEvent: ${event::class.simpleName}")

                                        try {
                                            // 🔥 【V2→V1提升完成】统一事件系统处理

                                            // 🔥 【架构统一】使用统一的映射器处理
                                            val mappingResult = domainMapper.mapSemanticToThinking(event, mappingContext)

                                            // 🔥 【状态更新】更新映射上下文
                                            mappingContext = mappingResult.context

                                            Timber
                                                .tag("TB-MAIN")
                                                .d("🗺️ [ThinkingBoxInstance] 映射得到${mappingResult.events.size}个ThinkingEvent: messageId=$messageId")
                                            // println("🗺️ [ThinkingBoxInstance] 映射得到${mappingResult.events.size}个ThinkingEvent")

                                            mappingResult.events.forEach { thinkingEvent ->
                                                Timber
                                                    .tag("TB-MAIN")
                                                    .v("🎯 [ThinkingBoxInstance] 处理ThinkingEvent: messageId=$messageId, event=${thinkingEvent::class.simpleName}")
                                                // println("🎯 [ThinkingBoxInstance] 处理ThinkingEvent: ${thinkingEvent::class.simpleName}")
                                                handleThinkingEvent(thinkingEvent)
                                            }
                                        } catch (e: Exception) {
                                            Timber.tag("TB-MAIN").e(
                                                e,
                                                "❌ [ThinkingBoxInstance] 系统处理失败: messageId=$messageId",
                                            )
                                            // println("❌ [ThinkingBoxInstance] 系统处理失败: ${e.message}")
                                        }
                                    } else {
                                        Timber
                                            .tag(TAG)
                                            .w("[$messageId] Received non-SemanticEvent: ${event::class.simpleName}")
                                    }
                                }
                            } catch (e: Exception) {
                                Timber
                                    .tag("TOKEN-FLOW")
                                    .e(e, "❌ [ThinkingBoxInstance] 事件流监听异常: messageId=$messageId")
                                throw e
                            }
                        }

                    // 🔥 【关键修复】等待两个处理协程完成
                    try {
                        parserJob?.join()
                        mapperJob?.join()
                    } catch (e: Exception) {
                        Timber.tag("TOKEN-FLOW")
                            .e(e, "❌ [ThinkingBoxInstance] 处理协程异常: messageId=$messageId")
                        throw e
                    }
                } catch (e: Exception) {
                    if (e is kotlinx.coroutines.CancellationException) {
                        Timber.tag(TAG).w("🔥 ConversationScope job被取消: messageId=$messageId")
                    } else {
                        Timber.tag(TAG).e(e, "🚨 ConversationScope处理异常: messageId=$messageId")
                        // 🔥 【V2系统提升】错误处理通过日志记录，V2系统暂不支持Error事件
                    }
                } finally {
                    isStarted.set(false)
                    Timber.tag(TAG).i("🚪 ConversationScope job已结束: messageId=$messageId")
                }
            }

        // 🔥 当 conversationScope 被取消时，parserJob 也会被自动取消
        conversationScope.coroutineContext[Job]?.invokeOnCompletion {
            Timber.tag(TAG).d("[$messageId] ConversationScope completed, instance processing finished.")
        }

        // 🔥 【Token流修复】等待订阅就绪后再返回Flow
        Timber.tag("TOKEN-FLOW").d("🔄 [ThinkingBoxInstance] 等待token订阅就绪: messageId=$messageId")
        // 在协程中等待订阅就绪
        conversationScope.launch {
            subscriptionReady.await()
        }
        Timber.tag("TOKEN-FLOW")
            .i("✅ [ThinkingBoxInstance] Token订阅就绪，返回UiState Flow: messageId=$messageId")

        // 🔥 【系统统一】返回基于统一状态的UiState Flow
        return _uiState.map { convertToStandardUiState(it) }
    }

    /**
     * 🔥 【系统统一】发送事件到此实例
     */
    fun sendEvent(event: ThinkingEvent) {
        handleThinkingEvent(event)
    }

    /**
     * 🔥 【系统统一】重置此实例的内部状态
     */
    private fun resetInternalState() {
        _uiState.value = ThinkingReducer.ThinkingUiState()
        // MappingContext已移除，DomainMapper现在是纯函数式映射
        Timber.tag(TAG).i("🔥 实例状态已重置: messageId=$messageId")
    }

    // 🔥 【系统统一】V1事件处理方法已移除，使用handleThinkingEvent

    /**
     * 🔥 【系统统一】处理思考事件（主事件处理方法）
     */
    private fun handleThinkingEvent(thinkingEvent: ThinkingEvent) {
        val oldState = _uiState.value
        // 🔥 【关键修复】ThinkingReducer是object，直接调用静态方法
        _uiState.value = ThinkingReducer.reduce(_uiState.value, thinkingEvent)
        val newState = _uiState.value

        // 🔥 事件的详细日志
        Timber.tag(
            "TB-MAIN",
        ).d(
            "🎯 [$messageId] 事件处理: ${thinkingEvent::class.simpleName} | version=${oldState.version} → ${newState.version}",
        )
        // println("🎯 [TB-MAIN] [$messageId] 事件处理: ${thinkingEvent::class.simpleName} | version=${oldState.version} → ${newState.version}")

        // 🔥 【架构统一】记录状态变化（MappingContext已移除）
        if (oldState.activePhaseId != newState.activePhaseId) {
            val newPhaseId = newState.activePhaseId
            Timber.tag(
                "TB-MAIN",
            ).d("🔄 [$messageId] activePhaseId变化: ${oldState.activePhaseId} → $newPhaseId")
            // MappingContext已移除，DomainMapper现在是纯函数式映射
        }

        // 🔥 【系统统一】处理特殊事件
        when (thinkingEvent) {
            is ThinkingEvent.FinalArrived -> {
                Timber.tag(
                    "TB-MAIN",
                ).i("🎯 [$messageId] FinalArrived: markdown长度=${thinkingEvent.markdown.length}")
                handleFinalAnswer(thinkingEvent)
            }
            is ThinkingEvent.PreThinkEnd -> {
                Timber.tag("TB-MAIN").i("🎯 [$messageId] PreThinkEnd: 预思考结束")
            }
            else -> {
                // 其他事件的通用处理
            }
        }

        // 🔥 【系统统一】发射事件到共享Flow，供HistorySaver监听
        conversationScope.launch {
            try {
                thinkingEventFlow.emit(thinkingEvent)
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "发射事件失败: messageId=$messageId")
            }
        }
    }

    /**
     * 🔥 【系统统一】处理最终答案事件
     */
    private fun handleFinalAnswer(event: ThinkingEvent.FinalArrived) {
        conversationScope.launch(kotlinx.coroutines.Dispatchers.Default) {
            try {
                Timber.tag(TAG).i("🔥 [$messageId] 开始处理FinalArrived事件")
                Timber
                    .tag("TB-MAIN")
                    .i(
                        "🎯 [ThinkingBoxInstance] FinalArrived处理: messageId=$messageId, markdown长度=${event.markdown.length}",
                    )

                val currentState = _uiState.value

                // 🔥 【Final转换修复】使用当前状态中的finalMarkdown，而不是event.markdown
                val finalContent = currentState.finalMarkdown ?: event.markdown

                // 🔥 【关键修复】确保最终markdown内容不为空
                if (finalContent.isBlank()) {
                    Timber
                        .tag("TB-MAIN")
                        .w(
                            "⚠️ [ThinkingBoxInstance] Final内容为空: messageId=$messageId, finalMarkdown长度=${currentState.finalMarkdown?.length}, event.markdown长度=${event.markdown.length}",
                        )
                    return@launch
                }

                // 🔥 【系统统一】触发AI消息完成回调，保存到数据库
                // 注意：系统暂时使用空的tokensSnapshot，后续可以从状态中获取
                val tokensSnapshot = "" // TODO: 从状态中获取tokens信息

                // 🔥 【最终富文本调试】详细记录回调触发过程
                Timber.tag("TB-INSTANCE").d("🎯 [AI消息完成] 准备调用onAiMessageComplete回调")
                Timber.tag(
                    "TB-INSTANCE",
                ).d("🎯 [AI消息完成] messageId=$messageId, finalContent长度=${finalContent.length}")
                Timber.tag("TB-INSTANCE").d("🎯 [AI消息完成] finalContent内容预览='${finalContent.take(100)}...'")
                Timber.tag(
                    "TB-INSTANCE",
                ).d("🎯 [AI消息完成] onAiMessageComplete回调是否存在: ${onAiMessageComplete != null}")

                Timber.tag("TB-MAIN")
                    .i("📊 [ThinkingBoxInstance] 准备调用onAiMessageComplete: messageId=$messageId")
                onAiMessageComplete?.invoke(messageId, finalContent, tokensSnapshot)
                Timber.tag("TB-MAIN")
                    .i("✅ [ThinkingBoxInstance] AI消息完成回调已触发: messageId=$messageId")

                Timber.tag("TB-INSTANCE").d("🎯 [AI消息完成] onAiMessageComplete回调已执行完成")

                Timber.tag("TB-MAIN").i("🏁 [ThinkingBoxInstance] 流式处理结束: messageId=$messageId")
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "🚨 [$messageId] FinalArrived处理失败")
                Timber.tag("TB-MAIN")
                    .e("❌ [ThinkingBoxInstance] FinalArrived处理异常: messageId=$messageId, error=${e.message}")
            }
        }
    }

    // 🔥 【系统统一】V1 handleFinalAnswer方法已移除，使用统一的handleFinalAnswer

    /**
     * 启动7秒倒计时
     */
    private fun startFinalRenderingTimeout() {
        finalRenderingTimeoutJob?.cancel()
        Timber.tag(TAG).i("🔥 [$messageId] 启动7秒倒计时")

        finalRenderingTimeoutJob =
            conversationScope.launch {
                try {
                    kotlinx.coroutines.delay(7_000L) // 🔥 【倒计时优化】15秒 → 7秒，提升用户体验
                    Timber.tag(TAG).i("🔥 [$messageId] 7秒倒计时结束")
                    // 🔥 【V2系统提升】V2系统不需要FinalRenderingTimeout事件，超时由状态管理
                } catch (e: kotlinx.coroutines.CancellationException) {
                    Timber.tag(TAG).d("🔥 [$messageId] 7秒倒计时被取消")
                } catch (e: Exception) {
                    Timber.tag(TAG).e(e, "🚨 [$messageId] 7秒倒计时异常")
                } finally {
                    finalRenderingTimeoutJob = null
                }
            }
    }

    /**
     * 清理此实例
     */
    fun cleanup() {
        Timber.tag(TAG).d("🔥 清理ThinkingBox实例: messageId=$messageId")

        parserJob?.cancel()
        parserJob = null

        finalRenderingTimeoutJob?.cancel()
        finalRenderingTimeoutJob = null

        isStarted.set(false)
    }

    /**
     * 检查实例是否已完成
     */
    fun isCompleted(): Boolean {
        val state = _uiState.value
        return !state.isStreaming && state.finalMarkdown != null
    }

    /**
     * 检查实例是否超过指定时间
     */
    fun isOlderThan(durationMs: Long): Boolean = System.currentTimeMillis() - createdAt > durationMs

    /**
     * 获取实例创建时间（用于调试）
     */
    fun getCreatedAt(): Long = createdAt

    /**
     * 🔥 【系统统一】获取当前状态转换为标准UiState
     */
    val currentState: UiState
        get() = convertToStandardUiState(_uiState.value)

    /**
     * 🔥 706任务保存：处理FinalArrived事件，触发历史保存
     *
     * 当AI消息的最终内容到达时，将其保存到历史记录中
     * 使用现有的HistorySaver进行ThinkingBox特定的历史保存
     */
    private fun handleFinalArrivedEvent(event: SemanticEvent.FinalArrived) {
        conversationScope.launch {
            try {
                Timber
                    .tag(TAG)
                    .d(
                        "🔥 [706任务保存] 处理FinalArrived事件: messageId=$messageId, content长度=${event.markdown.length}",
                    )

                // 🔥 【V2系统提升】收集V2状态的思考内容
                val currentState = _uiState.value
                val thinkingNodes =
                    try {
                        // 将V2思考阶段转换为JSON字符串保存
                        kotlinx.serialization.json.Json
                            .encodeToString(currentState.phases.values.toList())
                    } catch (e: Exception) {
                        Timber.tag(TAG).w(e, "序列化V2思考节点失败")
                        null
                    }

                // 🔥 【关键修复】通过回调触发包含完整思考内容的AI消息保存
                onAiMessageComplete?.invoke(messageId, event.markdown, thinkingNodes)

                // 🔥 【V2系统提升】V2系统不需要创建额外的事件，状态已由V2 Reducer管理

                Timber
                    .tag(TAG)
                    .d("✅ [706任务保存] FinalArrived事件已处理，AI消息内容和思考过程已传递给Coach保存: messageId=$messageId")
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "🚨 [706任务保存] 处理FinalArrived事件异常: messageId=$messageId")
            }
        }
    }

    // 🔥 【V2系统提升】V1超时检查方法已移除，V2系统使用不同的状态管理

    /**
     * 🔥 【超时机制优化】取消7秒倒计时
     */
    private fun cancelFinalRenderingTimeout(reason: String) {
        finalRenderingTimeoutJob?.let { job ->
            if (job.isActive) {
                job.cancel()
                Timber.tag(TAG).i("🔥 [$messageId] 7秒倒计时已取消: $reason")
            }
        }
        finalRenderingTimeoutJob = null
    }

    /**
     * 🔥 【系统统一】将状态转换为标准UiState
     */
    private fun convertToStandardUiState(state: ThinkingReducer.ThinkingUiState): UiState {
        // 🔥 【状态一致性验证】检查状态一致性
        val validationIssues = ThinkingReducer.validateState(state)
        if (validationIssues.isNotEmpty()) {
            Timber.tag("TB-STATE").w("⚠️ [状态一致性] 发现状态不一致问题: ${validationIssues.joinToString(", ")}")
        }
        // 转换phases从Map到List
        val phasesList = state.phases.values.map { phase ->
            com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi(
                id = phase.id,
                title = phase.title,
                content = phase.content,
                isComplete = phase.isComplete,
            )
        }.toList()

        // 🔥 【状态一致性修复】计算正确的elapsed时间
        val currentTime = System.currentTimeMillis()
        val actualElapsed = if (state.startTime > 0L) {
            if (state.isStreaming) {
                // 正在流式传输时，使用当前时间计算
                (currentTime - state.startTime).milliseconds
            } else {
                // 已完成时，使用记录的持续时间
                state.thinkingDuration.milliseconds
            }
        } else {
            Duration.ZERO
        }

        // 🔥 【双重处理修复】修复ThinkingHeader显示逻辑，统一使用phase架构
        val shouldShowHeader = when {
            // 如果有正式phase激活（非perthink），则不显示Header
            state.activePhaseId != null && state.activePhaseId != "perthink" -> false
            // 🔥 【双重处理修复】如果正在流式传输或有任何phase内容，则显示Header
            state.isStreaming || state.phases.isNotEmpty() -> true
            // 其他情况不显示
            else -> false
        }

        // 🔥 【双重处理修复】修复isThinking状态映射，统一使用phase架构
        val isActuallyThinking = state.isStreaming ||
            state.phases.any { !it.value.isComplete } ||
            (state.activePhaseId != null && !state.isThinkingComplete)

        // 🔥 【性能优化】移除详细日志，避免日志混乱

        // 🔥 【状态映射调试】记录映射结果
        val mappedUiState = UiState(
            phases = phasesList,
            finalMarkdown = state.finalMarkdown,
            isThinking = isActuallyThinking, // 🔥 【关键修复】使用正确的思考状态判断
            isCollapsed = false, // 系统默认不折叠
            elapsed = actualElapsed,
            showHeader = shouldShowHeader,
            visiblePhases = phasesList,
            activePhaseId = state.activePhaseId,
            isStreaming = state.isStreaming,
            final = state.finalMarkdown,
            preThinking = state.preThinking, // 🔥 【perthink激活修复】直接使用preThinking字段，确保perthink内容正确映射
            // 🔥 【时序协调修复】映射最终富文本渲染就绪状态
            finalRichTextReady = state.finalRichTextReady,

            // 🔥 【问题2修复】映射Final内容到达标志
            finalContentArrived = state.finalContentArrived,
            // 🔥 【统一实现】移除冗余的状态映射，简化状态管理
            // 🔥 【状态一致性修复】映射思考完成状态和token计算
            isThinkingComplete = state.isThinkingComplete,
            thinkingDuration = state.thinkingDuration,
            totalTokens = if (state.isThinkingComplete) {
                state.calculateTotalTokens(
                    tokenizerService,
                )
            } else {
                state.totalTokens
            },

            // 🔥 【717修复方案】映射新状态字段到UI层
            finalTokens = state.finalTokens, // TypewriterRenderer使用
            isFinalStreaming = state.isFinalStreaming, // Final流式状态

            // 🔥 【对话完成状态】映射对话完成相关状态
            isConversationComplete = state.isConversationComplete,
            showCopyButton = state.showCopyButton,
            shouldSaveHistory = state.shouldSaveHistory,

            // 🔥 【问题1修复】映射perthink完成状态
            perthinkCompleted = state.perthinkCompleted,
        )

        // 🔥 【性能优化】采样输出调试日志，避免FinalToken频繁触发重复日志
        val shouldLogStateMapping = (state.finalMarkdown?.length ?: 0) % 50 == 0 || // 每50字符采样一次
            state.finalRichTextReady || // 关键状态变化时输出
            state.isThinkingComplete // 思考完成时输出

        // 🔥 【性能优化】移除详细日志输出，仅在关键状态变化时记录
        if (state.finalRichTextReady) {
            Timber.tag(
                "TB-INSTANCE",
            ).i("🎯 [状态映射完成] finalRichTextReady=true, isThinkingComplete=${mappedUiState.isThinkingComplete}")
        }

        return mappedUiState
    }
}
