package com.example.gymbro.features.workout.template.edit.handlers

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.resources.ResourceProvider
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.model.template.toDomain
import com.example.gymbro.features.workout.template.cache.TemplateAutoSaveManager
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import com.example.gymbro.features.workout.template.edit.data.TemplateDataMapper
import com.example.gymbro.features.workout.template.edit.transaction.TemplateTransactionManager
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * TemplateEdit 保存处理器
 *
 * 🎯 职责：
 * - 统一的模板保存逻辑
 * - 自动保存管理
 * - 保存状态协调
 * - 错误处理和重试
 *
 * 🔥 重构改进：
 * - 从ViewModel中提取保存逻辑
 * - 移除过时的JSON处理（已由JSON系统处理）
 * - 简化保存流程
 * - 统一错误处理
 */
@Singleton
class TemplateEditSaveHandler @Inject constructor(
    private val templateTransactionManager: TemplateTransactionManager,
    private val autoSaveManager: TemplateAutoSaveManager,
    private val resourceProvider: ResourceProvider,
    private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase
) {
    // 🔥 添加互斥锁防止并发保存操作导致的竞态条件
    private val saveMutex = Mutex()

    /**
     * 🔥 核心保存方法
     * 简化的保存流程，移除过时的JSON处理步骤
     */
    suspend fun handleSave(
        currentState: TemplateEditContract.State,
        isDraft: Boolean,
        isPublishing: Boolean,
        onSuccess: (String, WorkoutTemplate) -> Unit,
        onError: (UiText) -> Unit
    ) = saveMutex.withLock {  // 🔥 使用互斥锁防止并发保存
        // 取消任何待处理的自动保存
        autoSaveManager.cancelPendingSaves()

        // 🔥 优化：区分新建和更新场景的验证逻辑
        val isNewTemplate = currentState.template?.id.isNullOrBlank() ||
                           currentState.template?.id?.startsWith("temp_") == true

        // 🔥 新增：严格的状态转换验证
        if (isDraft && currentState.isPublished == true) {
            Timber.e("🚫 状态转换错误：尝试将已发布模板保存为草稿")
            onError(UiText.DynamicString("已发布的模板不能退回到草稿状态"))
            return@withLock
        }

        // 验证基本信息
        if (currentState.templateName.isBlank() && !isDraft) {
            onError(UiText.DynamicString("模板名称不能为空"))
            return@withLock
        }

        // 🔥 新增：新建模板时的额外验证
        if (isNewTemplate && currentState.exercises.isEmpty()) {
            Timber.d("🔧 新建模板无动作，允许保存空模板作为草稿")
        }

        // 构建Domain模型
        val templateToSave = buildTemplateFromState(currentState, isDraft, isPublishing)

        Timber.d("🔧 保存模板: 新建=$isNewTemplate, 草稿=$isDraft, 发布=$isPublishing")

        // 执行保存
        executeSave(templateToSave, onSuccess, onError)
    }

    /**
     * 🔥 自动保存处理
     */
    suspend fun handleAutoSave(
        template: WorkoutTemplate,
        onSuccess: () -> Unit,
        onError: (UiText) -> Unit
    ) = saveMutex.withLock {  // 🔥 使用互斥锁防止与手动保存冲突
        executeSave(
            template = template.copy(isDraft = true),
            onSuccess = { _, _ -> onSuccess() },
            onError = onError
        )
    }

    /**
     * 🔥 构建Domain模板对象
     * 简化的构建逻辑，移除复杂的JSON转换步骤
     */
    private suspend fun buildTemplateFromState(
        state: TemplateEditContract.State,
        isDraft: Boolean,
        isPublishing: Boolean
    ): WorkoutTemplate {
        // 使用TemplateDataMapper进行状态到DTO的转换
        val templateDto = TemplateDataMapper.mapStateToDto(state)

        // 🔥 修复：获取当前用户ID，确保与查询时一致
        val currentUserId = try {
            val userIdResult = getCurrentUserIdUseCase().firstOrNull()
            Timber.tag("CRITICAL-SAVE").d("🔥 [USER-ID-DEBUG] getUserIdUseCase result: $userIdResult")
            
            when (userIdResult) {
                is ModernResult.Success -> {
                    val userId = userIdResult.data
                    Timber.tag("CRITICAL-SAVE").d("🔥 [USER-ID-DEBUG] 成功获取用户ID: $userId")
                    userId
                }
                is ModernResult.Error -> {
                    Timber.tag("CRITICAL-SAVE").e("🔥 [USER-ID-DEBUG] 获取用户ID失败: ${userIdResult.error}")
                    null
                }
                is ModernResult.Loading -> {
                    Timber.tag("CRITICAL-SAVE").w("🔥 [USER-ID-DEBUG] 用户ID仍在加载中")
                    null
                }
                null -> {
                    Timber.tag("CRITICAL-SAVE").w("🔥 [USER-ID-DEBUG] getUserIdUseCase返回null")
                    null
                }
            }
        } catch (e: Exception) {
            Timber.tag("CRITICAL-SAVE").e(e, "🔥 [USER-ID-DEBUG] 获取用户ID异常")
            null
        } ?: "system" // fallback到system作为最后手段

        Timber.tag("CRITICAL-SAVE").d("🔥 [USER-ID-DEBUG] 最终使用用户ID: $currentUserId")

        // 转换为Domain对象，使用正确的用户ID
        return templateDto.toDomain(currentUserId).copy(
            isDraft = isDraft,
            isPublished = isPublishing,
            updatedAt = System.currentTimeMillis()
        )
    }

    /**
     * 🔥 执行保存操作
     */
    private suspend fun executeSave(
        template: WorkoutTemplate,
        onSuccess: (String, WorkoutTemplate) -> Unit,
        onError: (UiText) -> Unit
    ) {
        try {
            Timber.tag("SAVE-HANDLER").i("🔥 开始保存模板: ${template.name}")

            // 使用事务管理器保存
            val saveResult = templateTransactionManager.saveTemplateAtomically(
                template = template,
                validateBeforeSave = true
            )

            when (saveResult) {
                is ModernResult.Success -> {
                    val savedTemplateId = saveResult.data
                    Timber.tag("SAVE-HANDLER").i("✅ 保存成功: templateId=$savedTemplateId")

                    // 通知自动保存管理器
                    autoSaveManager.notifyManualSaveCompleted()

                    onSuccess(savedTemplateId, template)
                }
                is ModernResult.Error -> {
                    val errorMsg = saveResult.error.uiMessage?.asString(resourceProvider) ?: "保存失败"
                    Timber.tag("SAVE-HANDLER").e("❌ 保存失败: $errorMsg")
                    onError(UiText.DynamicString("保存失败: $errorMsg"))
                }
                is ModernResult.Loading -> {
                    Timber.tag("SAVE-HANDLER").w("⚠️ 保存操作返回Loading状态")
                    onError(UiText.DynamicString("保存状态异常"))
                }
            }
        } catch (e: Exception) {
            Timber.tag("SAVE-HANDLER").e(e, "❌ 保存过程异常")
            onError(UiText.DynamicString("保存过程中发生异常: ${e.message}"))
        }
    }

    /**
     * 🔥 清理资源
     */
    fun cleanup() {
        // 取消所有待处理的保存操作
        autoSaveManager.cancelPendingSaves()
        Timber.d("🧹 TemplateEditSaveHandler 清理完成")
    }
}
