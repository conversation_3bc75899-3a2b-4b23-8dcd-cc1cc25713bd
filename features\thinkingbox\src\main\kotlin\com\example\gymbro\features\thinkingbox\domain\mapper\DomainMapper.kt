package com.example.gymbro.features.thinkingbox.domain.mapper

import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.constants.ThinkingBoxStrings
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * DomainMapper - 统一事件映射器
 *
 * 将SemanticEvent映射为ThinkingEvent的核心组件。
 * 支持6个统一事件：PreThinkChunk, PreThinkEnd, PhaseStart, PhaseContent, PhaseEnd, FinalArrived
 */
@Singleton
class DomainMapper @Inject constructor() {

    /**
     * 映射上下文
     */
    data class MappingContext(
        val currentPhaseId: String? = null,
        val lastPhaseId: String? = null, // 🔥 【Title解析修复】跟踪最近的phase，用于title解析
        val inThinkTag: Boolean = false,
        val inThinkingTag: Boolean = false,
        val inTitleTag: Boolean = false,
        val inFinalTag: Boolean = false,
        var hasPerthinkStarted: Boolean = false, // 🔥 【核心修复】跟踪perthink是否已开始
        val perthinkCompleted: Boolean = false, // 🔥 【状态锁定】跟踪perthink是否已完成，防止重新激活
        val titleBuffer: StringBuilder = StringBuilder(), // 🔥 【Title缓冲修复】缓冲标题内容
    )

    /**
     * 映射结果
     */
    data class MappingResult(
        val events: List<ThinkingEvent>,
        val context: MappingContext,
    )

    /**
     * 主映射方法
     */
    fun mapSemanticToThinking(
        event: SemanticEvent,
        context: MappingContext = MappingContext(),
    ): MappingResult {
        // 映射事件

        return when (event) {
            // ===== 6个统一事件系统映射 =====

            is SemanticEvent.PreThinkChunk -> {
                // 🔥 【统一事件1】PreThinkChunk 直接映射
                MappingResult(
                    events = listOf(ThinkingEvent.PreThinkChunk(event.text)),
                    context = context,
                )
            }

            is SemanticEvent.PreThinkEnd -> {
                // 🔥 【统一事件2】PreThinkEnd 直接映射
                MappingResult(
                    events = listOf(ThinkingEvent.PreThinkEnd),
                    context = context.copy(
                        inThinkTag = false,
                        perthinkCompleted = true,
                    ),
                )
            }

            is SemanticEvent.PhaseStart -> {
                // 🔥 【统一事件3】PhaseStart 直接映射
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseStart(event.id, event.title)),
                    context = context.copy(
                        currentPhaseId = event.id,
                        lastPhaseId = event.id, // 🔥 【Title解析修复】同时更新lastPhaseId
                        hasPerthinkStarted = if (event.id == "perthink") true else context.hasPerthinkStarted,
                    ),
                )
            }

            is SemanticEvent.PhaseContent -> {
                // 🔥 【统一事件4】PhaseContent 直接映射
                Timber.tag(
                    "TB-MAPPER",
                ).d("📝 处理PhaseContent: id=${event.id}, content=${event.content.take(50)}...")
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseContent(event.id, event.content)),
                    context = context,
                )
            }

            is SemanticEvent.PhaseEnd -> {
                // 🔥 【统一事件5】PhaseEnd 直接映射
                Timber.tag("TB-MAPPER").d("🔚 处理PhaseEnd: id=${event.id}")
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseEnd(event.id)),
                    context = context.copy(
                        currentPhaseId = if (context.currentPhaseId == event.id) null else context.currentPhaseId,
                    ),
                )
            }

            is SemanticEvent.PhaseTitleUpdate -> {
                // 🔥 【Title缓冲迁移】PhaseTitleUpdate 直接映射
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseTitleUpdate(event.phaseId, event.title)),
                    context = context,
                )
            }

            is SemanticEvent.FinalStart -> {
                // 🔥 【关键修复】最终内容开始，启动后台异步渲染
                Timber.tag("TB-MAPPER").i("🚨 FinalStart事件映射：启动后台异步渲染")
                Timber.tag("TB-MAPPER").i("🚨 将发送ThinkingEvent.FinalStart到Reducer")
                println("🚨 [TB-MAPPER] FinalStart事件映射：启动后台异步渲染")
                MappingResult(
                    events = listOf(ThinkingEvent.FinalStart),
                    context = context,
                )
            }

            is SemanticEvent.FinalChunk -> {
                // 🔥 【流式修复】流式最终内容块
                Timber.tag("TB-MAPPER").d("📄 Final chunk: ${event.content.take(50)}...")
                MappingResult(
                    events = listOf(ThinkingEvent.FinalToken(event.content)),
                    context = context,
                )
            }

            is SemanticEvent.FinalEnd -> {
                // 🔥 【717修复方案】最终内容结束，停止final流式状态
                Timber.tag("TB-MAPPER").i("🎯 FinalEnd事件：停止final流式状态")
                MappingResult(
                    events = listOf(ThinkingEvent.FinalEnd),
                    context = context,
                )
            }

            is SemanticEvent.FinalArrived -> {
                // 🔥 【向后兼容】保留对旧事件的支持
                Timber.tag("TB-MAPPER").w("📄 使用已弃用的FinalArrived事件: ${event.markdown.take(50)}...")
                MappingResult(
                    events = listOf(ThinkingEvent.FinalArrived(event.markdown)),
                    context = context,
                )
            }

            // ===== 向后兼容的XML标签事件 =====

            is SemanticEvent.TagOpened -> mapTagOpened(event, context)
            is SemanticEvent.TagClosed -> mapTagClosed(event, context)
            is SemanticEvent.TextChunk -> mapTextChunk(event, context)
            // ===== 辅助事件（不需要映射）=====

            is SemanticEvent.RawThinking,
            is SemanticEvent.RawThinkingChunk,
            is SemanticEvent.RawThinkingClosed,
            is SemanticEvent.StreamFinished,
            is SemanticEvent.FunctionCallDetected,
            is SemanticEvent.TokensSnapshot,
            is SemanticEvent.ParseErrorEvent,
            -> {
                // 这些事件不需要映射到 ThinkingEvent，直接忽略
                MappingResult(events = emptyList(), context = context)
            }
        }
    }

    /**
     * 处理标签开启事件
     */
    private fun mapTagOpened(
        event: SemanticEvent.TagOpened,
        context: MappingContext,
    ): MappingResult {
        return when (event.name) {
            "think" -> {
                // 🔥 【去掉中间转换层】<think>标签处理已移至StreamingThinkingMLParser，这里不再需要
                MappingResult(events = emptyList(), context = context)
            }

            "thinking" -> {
                // 🔥 【状态锁定修复】<thinking>标签开始，确保perthink状态被锁定
                MappingResult(
                    events = emptyList(),
                    context = context.copy(
                        inThinkingTag = true,
                        inThinkTag = false, // 🔥 【关键修复】清除inThinkTag状态
                        perthinkCompleted = true, // 🔥 【关键修复】锁定perthink状态
                    ),
                )
            }

            "phase" -> {
                val phaseId = event.attrs["id"] ?: "unknown"
                // 🔥 【数据接口一致性修复】TagOpened("phase")已被StreamingThinkingMLParser的PhaseStart替代
                // 这里只更新context状态，不重复创建phase
                MappingResult(
                    events = emptyList(), // 不重复创建phase
                    context = context.copy(
                        currentPhaseId = phaseId,
                        lastPhaseId = phaseId, // 🔥 【Title解析修复】同时更新lastPhaseId
                    ),
                )
            }

            "title" -> {
                // 🔥 【Title缓冲迁移】title标签处理已移至StreamingThinkingMLParser，这里不再需要
                MappingResult(events = emptyList(), context = context)
            }

            "final" -> {
                // 🔥 【修复】检测到<final>标签，立即发送FinalStart事件启动后台异步渲染
                Timber.tag("TB-MAPPER").i("🎯 检测到<final>标签，发送FinalStart事件启动后台异步渲染")
                MappingResult(
                    events = listOf(ThinkingEvent.FinalStart),
                    context = context.copy(inFinalTag = true),
                )
            }

            else -> {
                Timber.tag("TB-MAPPER").w("未知标签: ${event.name}")
                MappingResult(events = emptyList(), context = context)
            }
        }
    }

    /**
     * 处理标签关闭事件
     */
    private fun mapTagClosed(
        event: SemanticEvent.TagClosed,
        context: MappingContext,
    ): MappingResult {
        return when (event.name) {
            "think" -> {
                // 🔥 【去掉中间转换层】</think>标签处理已移至StreamingThinkingMLParser，这里不再需要
                MappingResult(events = emptyList(), context = context)
            }

            "thinking" -> {
                // 🔥 【修复16】</thinking>标签结束全部phase计算，发送ThinkingEnd事件
                Timber.tag("TB-MAPPER").d("🔚 </thinking>标签关闭，结束全部phase计算")
                MappingResult(
                    events = listOf(ThinkingEvent.ThinkingEnd),
                    context = context.copy(inThinkingTag = false),
                )
            }

            "phase" -> {
                val phaseId = context.currentPhaseId ?: "unknown"
                Timber.tag("TB-MAPPER").d("🏁 结束Phase: $phaseId, inThinkingTag=${context.inThinkingTag}")
                Timber.tag("TB-MAPPER").d("🔍 [Phase调试] 发送PhaseEnd事件: phaseId=$phaseId")
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseEnd(phaseId)),
                    context = context.copy(
                        currentPhaseId = null,
                        // 🔥 【Title解析修复】保持lastPhaseId，不清空，以便后续title解析使用
                    ),
                )
            }

            "title" -> {
                // 🔥 【Title缓冲迁移】title标签处理已移至StreamingThinkingMLParser，这里不再需要
                MappingResult(events = emptyList(), context = context)
            }

            "final" -> {
                MappingResult(
                    events = emptyList(),
                    context = context.copy(inFinalTag = false),
                )
            }

            else -> {
                Timber.tag("TB-MAPPER").w("未知标签关闭: ${event.name}")
                MappingResult(events = emptyList(), context = context)
            }
        }
    }

    /**
     * 处理文本块事件 - 🔥 【Header修复】核心修改点
     */
    private fun mapTextChunk(
        event: SemanticEvent.TextChunk,
        context: MappingContext,
    ): MappingResult {
        return when {
            // 🔥 【Phase内容显示修复】优先处理正式phase的内容，避免被perthink逻辑拦截
            context.currentPhaseId != null && context.inThinkingTag -> {
                // 🔥 【Phase统一架构】正式phase的内容
                Timber.tag(
                    "TB-MAPPER",
                ).d("📝 [优先级修复] Phase ${context.currentPhaseId} 内容: ${event.text.take(50)}...")
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseContent(context.currentPhaseId!!, event.text)),
                    context = context,
                )
            }

            // 🔥 【Title缓冲迁移】title文本处理已移至StreamingThinkingMLParser，这里不再需要
            // context.inTitleTag 条件已弃用

            context.inFinalTag -> {
                // 🔥 【流式修复】Final标签内的文本内容改为FinalChunk
                Timber.tag("TB-MAPPER").d("📄 Final chunk: ${event.text.take(50)}...")
                MappingResult(
                    events = listOf(ThinkingEvent.FinalToken(event.text)),
                    context = context,
                )
            }

            // 🔥 【唯一接口修复】perthink文本处理 - 基于currentPhaseId判断
            !context.inThinkingTag && !context.perthinkCompleted && context.currentPhaseId == "perthink" -> {
                val events = mutableListOf<ThinkingEvent>()
                // 1. 发送PreThinkChunk给Header显示
                events.add(ThinkingEvent.PreThinkChunk(event.text))

                // 2. 同时更新perthink phase的content
                events.add(ThinkingEvent.PhaseContent("perthink", event.text))

                MappingResult(
                    events = events,
                    context = context,
                )
            }

            else -> {
                // 🔥 【调试增强】详细记录被忽略的文本和当前状态
                Timber.tag(
                    "TB-MAPPER",
                ).w(
                    "❌ [文本被忽略] 文本: '${event.text.take(
                        50,
                    )}...', 状态: currentPhaseId=${context.currentPhaseId}, inThinkTag=${context.inThinkTag}, inThinkingTag=${context.inThinkingTag}, inTitleTag=${context.inTitleTag}, perthinkCompleted=${context.perthinkCompleted}",
                )
                MappingResult(events = emptyList(), context = context)
            }
        }
    }
}
