package com.example.gymbro.features.thinkingbox.domain.mapper

import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.constants.ThinkingBoxStrings
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * DomainMapper - 统一事件映射器
 *
 * 将SemanticEvent映射为ThinkingEvent的核心组件。
 * 支持6个统一事件：PreThinkChunk, PreThinkEnd, PhaseStart, PhaseContent, PhaseEnd, FinalArrived
 */
@Singleton
class DomainMapper @Inject constructor() {

    /**
     * 映射上下文 - 轻量级状态容器
     */
    data class MappingContext(
        val currentPhaseId: String? = null,
        val lastPhaseId: String? = null,          // 🔥 【Title解析修复】跟踪最近的phase，用于title解析
        val inThinkTag: Boolean = false,
        val inThinkingTag: Boolean = false,
        val inTitleTag: Boolean = false,
        val inFinalTag: Boolean = false,
        var hasPerthinkStarted: Boolean = false,  // 🔥 【核心修复】跟踪perthink是否已开始
        val perthinkCompleted: Boolean = false,   // 🔥 【状态锁定】跟踪perthink是否已完成，防止重新激活
        val titleBuffer: StringBuilder = StringBuilder() // 🔥 【Title缓冲修复】缓冲标题内容
    )

    /**
     * 映射结果
     */
    data class MappingResult(
        val events: List<ThinkingEvent>,
        val context: MappingContext
    )

    /**
     * 主映射方法
     */
    fun mapSemanticToThinking(
        event: SemanticEvent,
        context: MappingContext = MappingContext()
    ): MappingResult {
        // 🔥 【核心映射】记录关键事件
        if (event is SemanticEvent.PreThinkChunk || event is SemanticEvent.PhaseStart || event is SemanticEvent.FinalArrived) {
            Timber.tag("DomainMapper").e("🔥 [核心映射] 映射事件: ${event::class.simpleName}")
        }

        return when (event) {
            // ===== 6个统一事件系统映射 =====

            is SemanticEvent.PreThinkChunk -> {
                MappingResult(
                    events = listOf(ThinkingEvent.PreThinkChunk(event.text)),
                    context = context
                )
            }

            is SemanticEvent.PreThinkEnd -> {
                MappingResult(
                    events = listOf(ThinkingEvent.PreThinkEnd),
                    context = context.copy(
                        inThinkTag = false,
                        perthinkCompleted = true
                    )
                )
            }

            is SemanticEvent.PhaseStart -> {
                // 🔥 【统一事件3】PhaseStart 直接映射
                Timber.tag("TB-MAPPER").d("🎯 处理PhaseStart: id=${event.id}, title=${event.title}")
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseStart(event.id, event.title)),
                    context = context.copy(
                        currentPhaseId = event.id,
                        lastPhaseId = event.id,  // 🔥 【Title解析修复】同时更新lastPhaseId
                        hasPerthinkStarted = if (event.id == "perthink") true else context.hasPerthinkStarted
                    )
                )
            }

            is SemanticEvent.PhaseContent -> {
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseContent(event.id, event.content)),
                    context = context
                )
            }

            is SemanticEvent.PhaseEnd -> {
                // 🔥 【事件映射】PhaseEnd 直接映射
                Timber.tag("RAW-TOKEN-COLLECTOR").e("🔥 [事件映射] SemanticEvent.PhaseEnd → ThinkingEvent.PhaseEnd: id=${event.id}")
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseEnd(event.id)),
                    context = context.copy(
                        currentPhaseId = if (context.currentPhaseId == event.id) null else context.currentPhaseId
                    )
                )
            }

            is SemanticEvent.PhaseTitleUpdate -> {
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseTitleUpdate(event.phaseId, event.title)),
                    context = context
                )
            }

            is SemanticEvent.FinalStart -> {
                MappingResult(
                    events = listOf(ThinkingEvent.FinalStart),
                    context = context
                )
            }

            is SemanticEvent.FinalChunk -> {
                MappingResult(
                    events = listOf(ThinkingEvent.FinalToken(event.content)),
                    context = context
                )
            }

            is SemanticEvent.FinalEnd -> {
                MappingResult(
                    events = listOf(ThinkingEvent.FinalEnd),
                    context = context
                )
            }

            is SemanticEvent.FinalArrived -> {
                // 向后兼容已弃用的事件
                @Suppress("DEPRECATION")
                MappingResult(
                    events = listOf(ThinkingEvent.FinalArrived(event.markdown)),
                    context = context
                )
            }

            // ===== 向后兼容的XML标签事件 =====

            is SemanticEvent.TagOpened -> mapTagOpened(event, context)
            is SemanticEvent.TagClosed -> mapTagClosed(event, context)
            is SemanticEvent.TextChunk -> mapTextChunk(event, context)

            // ===== 辅助事件（不需要映射）=====

            is SemanticEvent.RawThinking,
            is SemanticEvent.RawThinkingChunk,
            is SemanticEvent.RawThinkingClosed,
            is SemanticEvent.StreamFinished,
            is SemanticEvent.FunctionCallDetected,
            is SemanticEvent.TokensSnapshot,
            is SemanticEvent.ParseErrorEvent,
            -> {
                // 这些事件不需要映射到ThinkingEvent
                MappingResult(events = emptyList(), context = context)
            }
        }
    }

    /**
     * 处理标签开启事件 - 向后兼容
     */
    private fun mapTagOpened(
        event: SemanticEvent.TagOpened,
        context: MappingContext
    ): MappingResult {
        return when (event.name) {
            "think" -> {
                // 🔥 【方案1：业务逻辑处理】<think>标签开始 perthink phase
                Timber.tag("TB-MAPPER").d("🎯 <think>标签开始，创建perthink phase")
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseStart("perthink", "Bro is thinking")),
                    context = context.copy(
                        currentPhaseId = "perthink",
                        lastPhaseId = "perthink",
                        inThinkTag = true,
                        hasPerthinkStarted = true
                    )
                )
            }

            "thinking" -> {
                // 🔥 【方案1：业务逻辑处理】<thinking>标签开始，结束perthink并切换状态
                val events = mutableListOf<ThinkingEvent>()

                // 如果当前在perthink阶段，先结束它
                if (context.currentPhaseId == "perthink") {
                    Timber.tag("TB-MAPPER").d("🔚 <thinking>标签检测到，结束perthink phase")
                    events.add(ThinkingEvent.PreThinkEnd)
                    events.add(ThinkingEvent.PhaseEnd("perthink"))
                }

                MappingResult(
                    events = events,
                    context = context.copy(
                        inThinkingTag = true,
                        inThinkTag = false,
                        currentPhaseId = null, // 清空当前phase，等待正式phase
                        perthinkCompleted = true
                    )
                )
            }

            "phase" -> {
                val phaseId = event.attrs["id"] ?: "unknown"
                val events = mutableListOf<ThinkingEvent>()

                // 🔥 【方案1：业务逻辑处理】处理phase切换
                if (phaseId == "perthink") {
                    // 处理 <phase id="perthink">，等同于 <think> 标签
                    Timber.tag("TB-MAPPER").d("🎯 检测到<phase id=\"perthink\">，创建perthink phase")
                    events.add(ThinkingEvent.PhaseStart("perthink", "Bro is thinking"))
                } else {
                    // 正式phase，先结束当前phase（如果有）
                    if (context.currentPhaseId != null) {
                        val currentPhaseId = context.currentPhaseId!!
                        Timber.tag("TB-MAPPER").d("🔄 检测到新phase: $phaseId，先结束当前phase: $currentPhaseId")

                        if (currentPhaseId == "perthink") {
                            events.add(ThinkingEvent.PreThinkEnd)
                        }
                        events.add(ThinkingEvent.PhaseEnd(currentPhaseId))
                    }

                    // 开始新的正式phase
                    Timber.tag("TB-MAPPER").d("🎯 开始新Phase: $phaseId")
                    events.add(ThinkingEvent.PhaseStart(phaseId, null))
                }

                MappingResult(
                    events = events,
                    context = context.copy(
                        currentPhaseId = phaseId,
                        lastPhaseId = phaseId,
                        hasPerthinkStarted = if (phaseId == "perthink") true else context.hasPerthinkStarted
                    )
                )
            }

            "title" -> {
                // 🔥 【Title解析调试】详细记录title标签检测
                Timber.tag("TB-TITLE").i("🏷️ [Title解析] 🎯 检测到<title>标签开始！")
                Timber.tag("TB-TITLE").i("🏷️ [Title解析] 当前状态: currentPhaseId=${context.currentPhaseId}, inThinkingTag=${context.inThinkingTag}")
                Timber.tag("TB-TITLE").i("🏷️ [Title解析] 设置inTitleTag=true，准备接收title文本")

                // 🔥 【Title缓冲修复】清空titleBuffer，准备接收新的标题内容
                context.titleBuffer.clear()
                Timber.tag("TB-TITLE").i("🏷️ [Title解析] titleBuffer已清空，准备缓冲完整标题")

                MappingResult(
                    events = emptyList(),
                    context = context.copy(inTitleTag = true)
                )
            }

            "final" -> {
                // 🔥 【重复事件修复】删除TagOpened("final")的FinalStart处理，避免重复
                // SemanticEvent.FinalStart已经在StreamingThinkingMLParser中处理了
                Timber.tag("RAW-TOKEN-COLLECTOR").w("🔥 [重复事件] 忽略TagOpened(final)，避免重复FinalStart事件")
                MappingResult(
                    events = emptyList(), // 不再发送重复的FinalStart事件
                    context = context.copy(inFinalTag = true)
                )
            }

            else -> {
                Timber.tag("TB-MAPPER").w("未知标签: ${event.name}")
                MappingResult(events = emptyList(), context = context)
            }
        }
    }

    /**
     * 处理标签关闭事件 - 向后兼容
     */
    private fun mapTagClosed(
        event: SemanticEvent.TagClosed,
        context: MappingContext
    ): MappingResult {
        return when (event.name) {
            "think" -> {
                // 🔥 【方案1：业务逻辑处理】</think>标签结束，但不立即结束perthink
                // perthink将通过<thinking>标签或流结束来结束
                Timber.tag("TB-MAPPER").d("🔚 </think>标签结束，等待<thinking>或流结束")
                MappingResult(
                    events = emptyList(),
                    context = context.copy(inThinkTag = false)
                )
            }

            "thinking" -> {
                // 🔥 【修复16】</thinking>标签结束全部phase计算，发送ThinkingEnd事件
                Timber.tag("TB-MAPPER").d("🔚 </thinking>标签关闭，结束全部phase计算")
                MappingResult(
                    events = listOf(ThinkingEvent.ThinkingEnd),
                    context = context.copy(inThinkingTag = false)
                )
            }

            "phase" -> {
                // 🔥 【方案1：业务逻辑处理】</phase>标签结束当前phase
                val phaseId = context.currentPhaseId ?: "unknown"
                Timber.tag("TB-MAPPER").d("🔚 </phase>标签结束，结束phase: $phaseId")
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseEnd(phaseId)),
                    context = context.copy(currentPhaseId = null)
                )
            }

            "title" -> {
                // 🔥 【Title缓冲修复】title标签结束，发送完整的标题更新事件
                val targetPhaseId = context.currentPhaseId ?: context.lastPhaseId
                val completeTitle = context.titleBuffer.toString()

                Timber.tag("TB-TITLE").i("🏷️ [Title缓冲] 🎯 </title>标签结束！")
                Timber.tag("TB-TITLE").i("🏷️ [Title缓冲] 完整标题: '$completeTitle', targetPhaseId=${targetPhaseId}")

                if (targetPhaseId != null && completeTitle.isNotBlank()) {
                    Timber.tag("TB-TITLE").i("🏷️ [Title缓冲] 🚀 发送完整的PhaseTitleUpdate事件")
                    MappingResult(
                        events = listOf(ThinkingEvent.PhaseTitleUpdate(targetPhaseId, completeTitle)),
                        context = context.copy(inTitleTag = false)
                    )
                } else {
                    Timber.tag("TB-TITLE").w("🏷️ [Title缓冲] ⚠️ 无法发送标题事件: targetPhaseId=${targetPhaseId}, title='$completeTitle'")
                    MappingResult(
                        events = emptyList(),
                        context = context.copy(inTitleTag = false)
                    )
                }
            }

            "final" -> {
                MappingResult(
                    events = emptyList(),
                    context = context.copy(inFinalTag = false)
                )
            }

            else -> {
                Timber.tag("TB-MAPPER").w("未知标签关闭: ${event.name}")
                MappingResult(events = emptyList(), context = context)
            }
        }
    }

    /**
     * 处理文本块事件 - 🔥 【Header修复】核心修改点
     */
    private fun mapTextChunk(
        event: SemanticEvent.TextChunk,
        context: MappingContext
    ): MappingResult {
        return when {
            // 🔥 【方案1：业务逻辑处理】perthink阶段的文本
            context.currentPhaseId == "perthink" && !context.perthinkCompleted -> {
                Timber.tag("TB-MAPPER").d("📝 perthink文本: ${event.text.take(50)}...")
                val events = mutableListOf<ThinkingEvent>()
                // 发送PreThinkChunk给Header显示
                events.add(ThinkingEvent.PreThinkChunk(event.text))
                // 同时更新perthink phase的content
                events.add(ThinkingEvent.PhaseContent("perthink", event.text))
                MappingResult(events = events, context = context)
            }

            // 🔥 【方案1：业务逻辑处理】正式phase的内容
            context.currentPhaseId != null && context.inThinkingTag -> {
                Timber.tag("TB-MAPPER").d("📝 Phase ${context.currentPhaseId} 内容: ${event.text.take(50)}...")
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseContent(context.currentPhaseId!!, event.text)),
                    context = context
                )
            }

            context.inTitleTag -> {
                // 🔥 【Title缓冲修复】在title标签内时，缓冲文本内容而不立即发送事件
                val targetPhaseId = context.currentPhaseId ?: context.lastPhaseId

                if (targetPhaseId != null) {
                    // 将文本追加到titleBuffer中
                    context.titleBuffer.append(event.text)
                    Timber.tag("TB-TITLE").i("🏷️ [Title缓冲] 追加文本: '${event.text}' -> titleBuffer: '${context.titleBuffer}'")
                    Timber.tag("TB-TITLE").i("🏷️ [Title缓冲] targetPhaseId=${targetPhaseId}, 缓冲长度=${context.titleBuffer.length}")

                    // 不发送事件，等待</title>标签时发送完整标题
                    MappingResult(
                        events = emptyList(),
                        context = context
                    )
                } else {
                    Timber.tag("TB-TITLE").e("🏷️ [Title缓冲] ❌ title文本但无法确定phaseId: '${event.text}'")
                    Timber.tag("TB-TITLE").e("🏷️ [Title缓冲] 上下文状态: currentPhaseId=${context.currentPhaseId}, lastPhaseId=${context.lastPhaseId}")
                    MappingResult(events = emptyList(), context = context)
                }
            }

            context.inFinalTag -> {
                // 🔥 【流式修复】Final标签内的文本内容改为FinalChunk
                Timber.tag("TB-MAPPER").d("📄 Final chunk: ${event.text.take(50)}...")
                MappingResult(
                    events = listOf(ThinkingEvent.FinalToken(event.text)),
                    context = context
                )
            }

            // 🔥 【唯一措施】不再处理<think>标签内的文本，perthink内容由PRE_THINK状态处理

            else -> {
                // 🔥 【调试增强】详细记录被忽略的文本和当前状态
                Timber.tag("TB-MAPPER").w("❌ [文本被忽略] 文本: '${event.text.take(50)}...', 状态: currentPhaseId=${context.currentPhaseId}, inThinkTag=${context.inThinkTag}, inThinkingTag=${context.inThinkingTag}, inTitleTag=${context.inTitleTag}, perthinkCompleted=${context.perthinkCompleted}")
                MappingResult(events = emptyList(), context = context)
            }
        }
    }
}


