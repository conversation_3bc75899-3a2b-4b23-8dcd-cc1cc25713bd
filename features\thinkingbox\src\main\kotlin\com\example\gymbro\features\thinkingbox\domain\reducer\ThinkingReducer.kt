package com.example.gymbro.features.thinkingbox.domain.reducer

import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.constants.ThinkingBoxStrings
import timber.log.Timber
import java.util.*

/**
 * ThinkingReducer - 双时序架构Reducer
 *
 * 处理ThinkingEvent事件，维护数据时序和渲染时序分离
 * 🔥 【文档规范修复】状态结构完全符合thinkingbox双时序baseline.md要求
 */
object ThinkingReducer {

    /**
     * ThinkingUiState - 符合双时序baseline文档规范的状态结构
     *
     * 🔥 【关键修复】按照文档要求使用：
     * - phases: LinkedHashMap<String, Phase> 确保阶段顺序
     * - pending: ArrayDeque<String> 优化队列性能
     */
    data class ThinkingUiState(
        // 🔥 【文档规范修复】核心状态字段 - 完全符合baseline文档
        val phases: LinkedHashMap<String, PhaseUi> = linkedMapOf(),
        val activePhaseId: String? = null, // 渲染中的阶段
        val pending: ArrayDeque<String> = ArrayDeque(), // 等待队列
        val preThinking: String? = null, // 预思考文本
        val finalMarkdown: String? = null, // 完整富文本
        val isStreaming: Boolean = true,

        // 🔥 【流式修复】最终内容缓冲区
        val finalBuffer: StringBuilder = StringBuilder(),

        // 🔥 【节流修复】最终内容更新节流
        val lastFinalUpdateTime: Long = 0L,

        // 🔥 【P0修复】Title 缓冲机制
        val deferredTitles: MutableMap<String, String> = mutableMapOf(),

        // 元数据字段
        val sessionId: String? = null,
        val startTime: Long = 0L,
        val version: Long = 0L, // 用于触发Compose重组

        // 🔥 【时序协调修复】最终富文本渲染就绪状态
        val finalRichTextReady: Boolean = false,

        // 🔥 【Final时序修复】Final内容到达标志
        val finalContentArrived: Boolean = false,

        // 🔥 【717修复方案】TypewriterRenderer核心字段
        val finalTokens: List<String> = emptyList(), // Final token流式列表
        val isFinalStreaming: Boolean = false, // Final流式状态

        // 🔥 【中优先级修复2】思考完成状态管理
        val isThinkingComplete: Boolean = false, // 是否完成所有思考阶段
        val thinkingDuration: Long = 0L, // 思考持续时间（毫秒）
        val totalTokens: Int = 0, // 总token数量

        // 🔥 【对话完成状态】最终富文本动画完成，整个对话结束
        val isConversationComplete: Boolean = false, // 是否完成整个对话（包括最终富文本动画）
        val showCopyButton: Boolean = false, // 是否显示复制按钮
        val shouldSaveHistory: Boolean = false, // 是否应该保存到历史记录

        // 🔥 【问题1修复】Perthink完成状态，防止异常回退
        val perthinkCompleted: Boolean = false,

        // 🔥 【MVI 2.0】背景状态管理
        val backgroundState:
        com.example.gymbro.features.thinkingbox.domain.interfaces.ThinkingBoxBackgroundState = com.example.gymbro.features.thinkingbox.domain.interfaces.ThinkingBoxBackgroundState(),
    ) {
        // 便利属性
        val hasFinal: Boolean get() = finalMarkdown != null

        // 🔥 【唯一真源性修复】移除重复的shouldShowCollapsed，统一使用UiState中的实现
        // 🔥 【唯一真源性修复】移除重复的getFormattedDuration，统一使用UiState中的实现
        // 🔥 【唯一真源性修复】移除重复的formattedThinkingDuration，统一使用UiState中的实现

        // 计算总token数量（基于所有phases内容和finalMarkdown）
        fun calculateTotalTokens(
            tokenizerService: com.example.gymbro.core.ai.tokenizer.TokenizerService? = null,
        ): Int {
            val phaseContent = phases.values.joinToString(" ") { "${it.title ?: ""} ${it.content}" }
            val finalContent = finalMarkdown ?: ""
            val preThinkContent = preThinking ?: ""
            val allContent = "$phaseContent $finalContent $preThinkContent"

            return if (tokenizerService != null && tokenizerService.isAvailable()) {
                tokenizerService.countTokens(allContent)
            } else {
                // 简单的token估算：平均4个字符为1个token
                (allContent.length + 3) / 4
            }
        }

        val isCompleted: Boolean get() = !isStreaming && hasFinal
        val activePhase: PhaseUi? get() = activePhaseId?.let { phases[it] }
        val hasPendingPhases: Boolean get() = pending.isNotEmpty()
    }

    /**
     * Phase UI数据结构
     */
    data class PhaseUi(
        val id: String,
        val title: String? = null,
        val content: String = "",
        val isComplete: Boolean = false,
    ) {
        val hasTitle: Boolean get() = title != null && title.isNotEmpty()
    }

    /**
     * 主Reducer函数 - 处理ThinkingEvent事件
     *
     * @param state 当前状态
     * @param event 事件
     * @return 新状态
     */
    fun reduce(state: ThinkingUiState, event: ThinkingEvent): ThinkingUiState {
        Timber.tag("TB-REDUCER").d("Processing: ${event::class.simpleName}")

        return when (event) {
            is ThinkingEvent.PreThinkChunk -> {
                // 预思考内容现在通过perthink phase处理，保留此事件以防兼容性问题
                state.copy(
                    preThinking = (state.preThinking ?: "") + event.content,
                    isStreaming = true,
                    version = state.version + 1,
                )
            }

            is ThinkingEvent.PreThinkEnd -> {
                // 🔥 【finalmermaid大纲修复】按照文档第453-463行要求：PreThinkEnd能够立即切换到等待的正式phase
                val existingPerthinkPhase = state.phases["perthink"]
                if (existingPerthinkPhase != null) {
                    val updatedPhases = LinkedHashMap(state.phases).apply {
                        put("perthink", existingPerthinkPhase.copy(isComplete = true))
                    }

                    Timber.tag("TB-REDUCER").d("🔥 [PreThinkEnd] 标记perthink phase完成，content: ${existingPerthinkPhase.content.take(50)}...")

                    // 🔥 【文档要求】检查是否有等待的正式phase需要激活
                    val nextPhaseId = state.pending.firstOrNull()
                    val shouldSwitchToNextPhase = nextPhaseId != null && state.activePhaseId == "perthink"

                    if (shouldSwitchToNextPhase) {
                        // 🔥 【关键修复】perthink完成且有等待的正式phase时，立即切换
                        val newPending = ArrayDeque(state.pending).apply { removeFirst() }

                        Timber.tag("TB-REDUCER").i("🔄 [PreThinkEnd时序] perthink完成，立即切换到正式phase: $nextPhaseId")

                        state.copy(
                            preThinking = null,
                            phases = updatedPhases,
                            activePhaseId = nextPhaseId, // 🔥 【文档要求】立即切换到下一个phase
                            pending = newPending,
                            perthinkCompleted = true,
                            version = state.version + 1,
                        )
                    } else {
                        // 🔥 【保持兼容】没有等待phase时保持原逻辑
                        state.copy(
                            preThinking = null,
                            phases = updatedPhases,
                            perthinkCompleted = true,
                            version = state.version + 1,
                        )
                    }
                } else {
                    // 如果perthink phase不存在，创建一个空的并标记完成
                    Timber.tag("TB-REDUCER").w("PreThinkEnd时perthink phase不存在，创建空phase")
                    val perthinkPhase = PhaseUi(
                        id = "perthink",
                        title = ThinkingBoxStrings.PERTHINK_TITLE,
                        content = state.preThinking ?: "",
                        isComplete = true
                    )
                    val updatedPhases = LinkedHashMap(state.phases).apply {
                        put("perthink", perthinkPhase)
                    }

                    state.copy(
                        preThinking = null,
                        phases = updatedPhases,
                        perthinkCompleted = true, // 🔥 【问题1修复】标记perthink已完成，防止异常回退
                        // 🔥 【问题1修复】PreThinkEnd不应该设置activePhaseId，让正式phase自然激活
                        // activePhaseId 保持当前值，等待下一个正式phase激活
                        version = state.version + 1
                    )
                }
            }

            is ThinkingEvent.PhaseStart -> {
                // 🔥 【Title解析调试】详细记录PhaseStart事件处理
                Timber.tag(
                    "TB-TITLE",
                ).i("🏷️ [Title解析] PhaseStart事件: phaseId=${event.id}, event.title='${event.title}'")
                Timber.tag("TB-TITLE").i("🏷️ [Title解析] 当前缓存的titles: ${state.deferredTitles}")

                // 🔥 【P0修复】检查是否有缓存的title
                val deferredTitle = state.deferredTitles.remove(event.id)
                val finalTitle = deferredTitle ?: event.title

                // 🔥 【Title解析调试】记录title选择结果
                Timber.tag(
                    "TB-TITLE",
                ).i("🏷️ [Title解析] deferredTitle='$deferredTitle', finalTitle='$finalTitle'")

                val newPhase = PhaseUi(id = event.id, title = finalTitle)
                // 🔥 【文档规范修复】使用LinkedHashMap确保阶段顺序
                val updatedPhases = LinkedHashMap(state.phases).apply {
                    put(event.id, newPhase)
                }

                // 🔥 【P0修复】记录缓存title的应用
                if (deferredTitle != null) {
                    Timber.tag("TB-TITLE").i("♻️ [Title显示] 应用延迟title: ${event.id}='$deferredTitle'")
                }

                Timber.tag(
                    "TB-REDUCER",
                ).d(
                    "🔍 [Phase调试] PhaseStart事件: phaseId=${event.id}, title=${event.title}, activePhaseId=${state.activePhaseId}",
                )

                // 🔥 【中优先级修复2】记录思考开始时间（只在第一个非perthink phase时记录）
                val shouldRecordStartTime = state.startTime == 0L && event.id != "perthink"
                val newStartTime = if (shouldRecordStartTime) System.currentTimeMillis() else state.startTime

                // 🔥 【P0修复】计算版本增量（应用缓存title时额外+1）
                val versionIncrement = if (deferredTitle != null) 2 else 1

                // 🔥 【问题1修复】perthink 也遵循正常的 phase 激活逻辑
                if (event.id == "perthink") {
                    Timber.tag("TB-REDUCER").w("🚨 [perthink调试] PhaseStart perthink事件处理开始")
                    Timber.tag("TB-REDUCER").w("🚨 [perthink调试] → 当前activePhaseId=${state.activePhaseId}")
                    Timber.tag("TB-REDUCER").w("🚨 [perthink调试] → finalTitle='$finalTitle'")
                    Timber.tag("TB-REDUCER").w("🚨 [perthink调试] → 创建的phase: id=${newPhase.id}, title='${newPhase.title}'")

                    // perthink 在没有活跃 phase 时直接激活，有活跃 phase 时进入队列
                    if (state.activePhaseId == null) {
                        Timber.tag("TB-REDUCER").w("🚨 [perthink调试] Perthink直接激活（无活跃phase）")
                        val newState = state.copy(
                            phases = updatedPhases,
                            activePhaseId = "perthink",
                            isStreaming = true,
                            startTime = newStartTime,
                            version = state.version + versionIncrement,
                        )
                        Timber.tag("TB-REDUCER").w("🚨 [perthink调试] → 新状态: activePhaseId=${newState.activePhaseId}, phases=${newState.phases.keys}")
                        newState
                    } else {
                        Timber.tag("TB-REDUCER").w("🚨 [perthink调试] Perthink进入队列（有活跃phase: ${state.activePhaseId}）")
                        val newPending = ArrayDeque(state.pending).apply {
                            if ("perthink" !in this && "perthink" != state.activePhaseId) {
                                addLast("perthink")
                            }
                        }
                        val newState = state.copy(
                            phases = updatedPhases,
                            pending = newPending,
                            startTime = newStartTime,
                            version = state.version + versionIncrement,
                        )
                        Timber.tag("TB-REDUCER").w("🚨 [perthink调试] → 新状态: pending=${newState.pending.toList()}, phases=${newState.phases.keys}")
                        newState
                    }
                } else if (state.activePhaseId == null) {
                    // 没有活跃phase时，立即激活
                    Timber.tag("TB-REDUCER").d("🎯 [Phase调试] 无活跃phase，立即激活: ${event.id}")
                    state.copy(
                        phases = updatedPhases,
                        activePhaseId = event.id,
                        isStreaming = true,
                        startTime = newStartTime,
                        version = state.version + versionIncrement,
                    )
                } else {
                    // 🔥 【修复29】有活跃phase时，正式phase加入pending队列
                    val newPending = ArrayDeque(state.pending).apply {
                        if (event.id !in this && event.id != state.activePhaseId) {
                            addLast(event.id)
                            Timber.tag(
                                "TB-REDUCER",
                            ).d("🔥 [Phase调试] Phase ${event.id} 加入pending队列，当前队列: ${this.toList()}")
                        }
                    }

                    state.copy(
                        phases = updatedPhases,
                        pending = newPending,
                        startTime = newStartTime,
                        version = state.version + versionIncrement,
                    )
                }
            }

            is ThinkingEvent.PhaseTitleUpdate -> {
                // 🔥 【Title修复】阶段标题更新
                val existingPhase = state.phases[event.id]
                Timber.tag(
                    "TB-TITLE",
                ).i(
                    "🏷️ [Title显示] PhaseTitleUpdate事件: phaseId=${event.id}, title='${event.title}', phase存在=${existingPhase != null}",
                )
                Timber.tag("TB-TITLE").i("🏷️ [Title显示] 当前所有phases: ${state.phases.keys.toList()}")

                if (existingPhase != null) {
                    val updatedPhase = existingPhase.copy(
                        title = event.title,
                    )
                    // 🔥 【文档规范修复】使用LinkedHashMap确保阶段顺序
                    val updatedPhases = LinkedHashMap(state.phases).apply {
                        put(event.id, updatedPhase)
                    }

                    Timber.tag(
                        "TB-TITLE",
                    ).i("✅ [Title显示] Phase ${event.id} 标题更新成功: '${event.title}' → UI应该刷新")

                    state.copy(
                        phases = updatedPhases,
                        version = state.version + 1,
                    )
                } else {
                    // 🔥 【P0修复】Phase不存在时缓存title，不直接丢弃
                    Timber.tag("TB-TITLE").w("⏳ [Title解析] Phase未就绪, 缓存title: ${event.id} -> '${event.title}'")
                    state.deferredTitles[event.id] = event.title
                    Timber.tag("TB-TITLE").i("🏷️ [Title解析] 缓存后的titles: ${state.deferredTitles}")

                    // 🔥 【v1监控】记录title缓冲命中 - 通过日志记录，避免依赖注入复杂性
                    Timber.tag("TB-METRICS").i("📊 TITLE_DEFERRED_HIT: ${event.id}")

                    state // 不提升 version
                }
            }

            is ThinkingEvent.PhaseContent -> {
                // 🔥 【修复20】阶段内容追加更新，支持增量文本追加
                val existingPhase = state.phases[event.id]
                Timber.tag(
                    "TB-REDUCER",
                ).d(
                    "🔍 [Phase调试] PhaseContent事件: phaseId=${event.id}, content长度=${event.content.length}, phase存在=${existingPhase != null}",
                )

                if (existingPhase != null) {
                    val updatedPhase = existingPhase.copy(
                        content = existingPhase.content + event.content, // 追加内容
                    )
                    // 🔥 【文档规范修复】使用LinkedHashMap确保阶段顺序
                    val updatedPhases = LinkedHashMap(state.phases).apply {
                        put(event.id, updatedPhase)
                    }

                    Timber.tag(
                        "TB-REDUCER",
                    ).d(
                        "📝 [Phase调试] Phase ${event.id} 内容追加: +${event.content.length}字符, 总长度=${updatedPhase.content.length}",
                    )

                    state.copy(
                        phases = updatedPhases,
                        // 🔥 【性能优化】PhaseContent不频繁更新version，避免过度重组
                    )
                } else {
                    // Phase不存在，记录警告但不更新状态
                    Timber.tag(
                        "TB-REDUCER",
                    ).w(
                        "❌ [Phase调试] Content for non-existent phase: ${event.id}, 可用phases: ${state.phases.keys}",
                    )
                    state
                }
            }

            is ThinkingEvent.PhaseEnd -> {
                // 🔥 【问题3修复】PhaseEnd事件只标记阶段完成，不切换activePhaseId
                // activePhaseId的切换由PhaseAnimFinished事件触发，遵循双时序架构
                val phase = state.phases[event.id]
                Timber.tag(
                    "TB-REDUCER",
                ).d(
                    "🔍 [Phase调试] PhaseEnd事件: phaseId=${event.id}, phase存在=${phase != null}, activePhaseId=${state.activePhaseId}",
                )

                if (phase != null) {
                    // 🔥 【文档规范修复】使用LinkedHashMap确保阶段顺序
                    val updatedPhases = LinkedHashMap(state.phases).apply {
                        put(event.id, phase.copy(isComplete = true))
                    }

                    Timber.tag(
                        "TB-REDUCER",
                    ).d("🏁 [Phase调试] Phase ${event.id} 标记为完成，content长度=${phase.content.length}, 等待动画完成")

                    state.copy(
                        phases = updatedPhases,
                        version = state.version + 1,
                    )
                } else {
                    // 🔥 【P0修复】禁止创建ghost phase，记录警告并忽略
                    Timber.tag(
                        "TB-REDUCER",
                    ).w("⚠️ [Phase调试] PhaseEnd for non-existent phase: ${event.id}, 忽略该事件")

                    // 🔥 【v1监控】记录ghost phase警告 - 通过日志记录，避免依赖注入复杂性
                    Timber.tag("TB-METRICS").w("📊 GHOST_PHASE_WARNING: ${event.id}")

                    state
                }
            }

            // 🔥 【719施工方案修复】移除旧的PhaseAnimationFinished事件处理
            // 统一使用PhaseAnimFinished事件，避免双重处理逻辑

            is ThinkingEvent.PhaseAnimFinished -> {
                // 🔥 【步骤3修复】双重检查切换：当前激活 + 已完成
                val currentPhase = state.phases[event.id]
                Timber.tag("TB-REDUCER").d("🔍 [Phase调试] PhaseAnimFinished事件: phaseId=${event.id}")
                Timber.tag("TB-REDUCER").d("🔍 [Phase调试] 双重检查条件: activePhaseId=${state.activePhaseId}, isComplete=${currentPhase?.isComplete}")
                Timber.tag("TB-REDUCER").d("🔍 [Phase调试] pending队列: ${state.pending.toList()}")

                // 🔥 【P0修复】早到动画守卫
                if (currentPhase != null && !currentPhase.isComplete) {
                    Timber.tag("TB-REDUCER").w("⚠️ [Phase调试] 动画先于 PhaseEnd: ${event.id}")

                    // 🔥 【v1监控】记录动画先于PhaseEnd的情况 - 通过日志记录，避免依赖注入复杂性
                    Timber.tag("TB-METRICS").w("📊 ANIM_FINISH_BEFORE_END: ${event.id}")
                }

                if (event.id == state.activePhaseId && currentPhase?.isComplete == true) {
                    // 🔥 【文档规范修复】使用ArrayDeque优化队列操作
                    val nextPhaseId = state.pending.peekFirst()
                    val newPending = ArrayDeque(state.pending).apply {
                        if (isNotEmpty()) pollFirst()
                    }

                    Timber.tag("TB-REDUCER").i("🔥 [Phase切换] 双重检查通过 ${event.id} (激活+完成), 切换到: $nextPhaseId")
                    Timber.tag("TB-REDUCER").i("🔥 [Phase切换] pending队列: ${state.pending.toList()}")
                    Timber.tag("TB-REDUCER").i("🔥 [Phase切换] 所有phases状态: ${state.phases.map { "${it.key}:${it.value.isComplete}" }}")
                    Timber.tag("TB-REDUCER").i("🔥 [Phase切换] preThinking存在: ${state.preThinking != null}")

                    // 🔥 【问题1&2修复】检测思考是否完成 - 修正判断逻辑
                    val isThinkingComplete = nextPhaseId == null && // 没有下一个phase
                            state.phases.all { it.value.isComplete } // 所有phase都完成
                    // 🔥 【关键修复】移除 preThinking == null 条件，preThinking 不影响思考完成状态

                    Timber.tag("TB-REDUCER").i("🔥 [Phase切换] isThinkingComplete: $isThinkingComplete")

                    // 计算思考时长和token数量
                    val currentTime = System.currentTimeMillis()
                    val thinkingDuration = if (state.startTime > 0) currentTime - state.startTime else 0L
                    // 注意：这里不传递tokenizerService，因为Reducer是纯函数，不应该依赖外部服务
                    // token计算将在ThinkingBoxInstance中进行
                    val totalTokens = if (isThinkingComplete) state.calculateTotalTokens() else state.totalTokens

                    val newState = state.copy(
                        activePhaseId = nextPhaseId,
                        pending = newPending,
                        isThinkingComplete = isThinkingComplete,
                        thinkingDuration = thinkingDuration,
                        totalTokens = totalTokens,
                        // 🔥 【修复方案】移除直接设置finalRichTextReady，让UI层的FinalRenderingReady事件控制
                        // finalRichTextReady 应该只有在收到 FinalRenderingReady 事件时才设置为 true
                        version = state.version + 1
                    )

                    if (isThinkingComplete) {
                        Timber.tag("TB-REDUCER").i("🎯 思考完成！所有phase已完成，准备折叠思考框")
                        if (!state.finalMarkdown.isNullOrBlank()) {
                            Timber.tag("TB-REDUCER").i("🎯 [双时序架构] UI时序传递：phase→final，final内容长度=${state.finalMarkdown.length}")
                        }
                    }

                    newState
                } else {
                    // 🔥 【步骤3修复】双重检查失败，保持等待
                    val reason = when {
                        event.id != state.activePhaseId -> "非激活phase: ${event.id}, 当前激活: ${state.activePhaseId}"
                        currentPhase?.isComplete != true -> "phase未完成: ${event.id}"
                        else -> "未知原因"
                    }
                    Timber.tag("TB-REDUCER").d("PhaseAnimFinished双重检查失败: $reason，保持等待")
                    state
                }
            }

            is ThinkingEvent.ShowSummaryPanel -> {
                // 🔥 【UI交互事件】显示摘要面板
                Timber.tag("TB-REDUCER").i("🎯 [UI交互] ShowSummaryPanel事件：用户点击摘要，显示面板")
                // 这个事件主要用于UI层处理，Reducer可以记录状态但不需要特殊处理
                state.copy(
                    version = state.version + 1,
                )
            }

            is ThinkingEvent.SummaryAnimationComplete -> {
                // 🔥 【动画完成事件】摘要动画完成
                Timber.tag("TB-REDUCER").i("🎯 [动画完成] SummaryAnimationComplete事件：摘要显示动画完成")
                state.copy(
                    version = state.version + 1,
                )
            }

            is ThinkingEvent.SummaryCardCollapsed -> {
                // 🔥 【冲突修复】废弃事件：摘要显示逻辑已移除，此事件不再需要
                Timber.tag("TB-REDUCER").w("⚠️ [废弃事件] SummaryCardCollapsed事件已废弃，摘要显示逻辑已移除")

                // 保持状态不变，避免意外的状态修改
                state
            }

            is ThinkingEvent.ThinkingEnd -> {
                // 🔥 【时序匹配修复】</thinking>标签检测：正确处理思考完成到final的过渡
                Timber.tag("TB-REDUCER").i("🎯 [ThinkingEnd时序] 检测到</thinking>标签，处理思考完成状态")

                // 🔥 【关键逻辑】检查是否还有未完成的phase需要等待
                val hasIncompletePhases = state.phases.values.any { !it.isComplete }
                val hasActivePhasePending = state.activePhaseId != null

                if (hasIncompletePhases || hasActivePhasePending) {
                    // 🔥 【时序协调】如果还有未完成的phase，标记准备完成但不立即设置完成状态
                    Timber.tag("TB-REDUCER").i(
                        "🎯 [ThinkingEnd时序] 有未完成phase，等待完成：activePhase=${state.activePhaseId}, incomplete=${state.phases.filter { !it.value.isComplete }.keys}"
                    )

                    state.copy(
                        isStreaming = false, // 🔥 【数据状态】结束流式状态
                        // 🔥 【保持等待】isThinkingComplete和activePhaseId等待最后一个phase的PhaseAnimFinished
                        version = state.version + 1,
                    )
                } else {
                    // 🔥 【直接完成】没有未完成phase时，直接设置思考完成
                    Timber.tag("TB-REDUCER").i("🎯 [ThinkingEnd时序] 无未完成phase，直接设置思考完成")

                    state.copy(
                        isStreaming = false,
                        isThinkingComplete = true,
                        activePhaseId = null,
                        version = state.version + 1,
                    )
                }
            }

            is ThinkingEvent.FinalStart -> {
                // 🔥 【双时序架构】<final>标签检测：立即开始后台渲染，接收token流
                Timber.tag("TB-REDUCER").i("🚨 [关键修复] FinalStart事件：检测到<final>标签，立即开始后台final渲染")
                Timber.tag(
                    "TB-REDUCER",
                ).i("🚨 [关键修复] 当前状态：isStreaming=${state.isStreaming}, activePhaseId=${state.activePhaseId}")
                Timber.tag(
                    "TB-REDUCER",
                ).i("🚨 [关键修复] 设置finalContentArrived=true，开启后台渲染，等待phase UI完成后触发前台UI显示")

                state.copy(
                    // 🔥 【数据状态】结束数据流式状态
                    isStreaming = false,
                    // 🔥 【后台渲染】立即开启Final流式通道（后台渲染）
                    isFinalStreaming = true,
                    // 🔥 【双时序-数据】标记final内容开始到达，后台开始渲染
                    finalContentArrived = true, // 🔥 【关键修复】这个字段是核心，确保双时序条件满足
                    // 🔥 【关键修复】不设置finalRichTextReady，UI显示由phase完成触发
                    version = state.version + 1,
                )
            }

            is ThinkingEvent.FinalToken -> {
                // 🔥 【717修复方案】Final token流式处理：累积到finalTokens列表用于TypewriterRenderer
                Timber.tag("TB-REDUCER").i("📄 [Final调试] Final token到达: 长度=${event.content.length}")
                Timber.tag("TB-REDUCER").d("📄 [Final调试] Final token内容: ${event.content.take(100)}...")

                // 将token添加到finalTokens列表（TypewriterRenderer使用）
                val updatedTokens = state.finalTokens + event.content

                // 同时更新finalBuffer和finalMarkdown（向后兼容）
                val updatedBuffer = StringBuilder(state.finalBuffer).apply {
                    append(event.content)
                }
                val currentFinalMarkdown = updatedBuffer.toString()

                Timber.tag("TB-REDUCER").i("📄 [Final调试] Final内容累积完成:")
                Timber.tag("TB-REDUCER").i("📄 [Final调试] - finalTokens数量: ${updatedTokens.size}")
                Timber.tag("TB-REDUCER").i("📄 [Final调试] - 当前总长度: ${currentFinalMarkdown.length}")
                Timber.tag("TB-REDUCER").i("📄 [Final调试] - isFinalStreaming: ${state.isFinalStreaming}")
                Timber.tag("TB-REDUCER").d("📄 [Final调试] - 内容预览: ${currentFinalMarkdown.take(200)}...")

                state.copy(
                    finalTokens = updatedTokens, // 🔥 【717修复方案】TypewriterRenderer使用
                    isFinalStreaming = true, // 🔥 【717修复方案】激活流式状态
                    finalContentArrived = true, // 🔥 【兜底修复】确保final内容到达标志被设置
                    finalBuffer = updatedBuffer, // 向后兼容
                    finalMarkdown = currentFinalMarkdown, // 向后兼容
                    version = state.version + 1,
                )
            }

            is ThinkingEvent.FinalEnd -> {
                // 🔥 【717修复方案】</final>标签结束：停止final流式状态
                Timber.tag("TB-REDUCER").i("🎯 [Final时序] FinalEnd事件：检测到</final>标签，停止final流式状态")
                Timber.tag(
                    "TB-REDUCER",
                ).i(
                    "🎯 [Final时序] 当前状态：isFinalStreaming=${state.isFinalStreaming}, finalTokens数量=${state.finalTokens.size}",
                )

                state.copy(
                    isFinalStreaming = false, // 🔥 【717修复方案】停止final流式状态
                    isStreaming = false, // 停止整体流式状态
                    version = state.version + 1,
                )
            }

            is ThinkingEvent.FinalRenderingReady -> {
                // 🔥 【v2方案】UI层在最后一个formal phase动画完成后发送，触发TypewriterRenderer挂载
                Timber.tag("TB-REDUCER").i("🎯 [Final时序] FinalRenderingReady事件：UI层准备就绪，激活final渲染")
                Timber.tag(
                    "TB-REDUCER",
                ).i(
                    "🎯 [Final时序] 当前状态：finalRichTextReady=${state.finalRichTextReady}, finalContentArrived=${state.finalContentArrived}",
                )

                state.copy(
                    finalRichTextReady = true, // 激活final渲染
                    version = state.version + 1,
                )
            }

            is ThinkingEvent.FinalAnimationComplete -> {
                // 🔥 【对话完成】最终富文本动画完成，标记整个对话结束
                Timber.tag("TB-REDUCER").i("🎯 [FinalAnimationComplete] 最终富文本动画完成！标记对话结束，停止token计算，显示复制按钮")

                state.copy(
                    isConversationComplete = true, // 标记对话完成
                    showCopyButton = true, // 显示复制按钮
                    shouldSaveHistory = true, // 标记需要保存历史记录
                    version = state.version + 1,
                )
            }

            is ThinkingEvent.FinalArrived -> {
                // 🔥 【向后兼容】保留对已弃用FinalArrived事件的支持
                val currentTime = System.currentTimeMillis()
                val thinkingDuration = if (state.startTime > 0) currentTime - state.startTime else 0L
                val totalTokens = state.calculateTotalTokens()

                // 🔥 【向后兼容】简化FinalArrived处理日志，避免与Instance层重复
                Timber.tag("TB-REDUCER").d("🎯 [FinalArrived] 处理已弃用事件: 长度=${event.markdown.length}")

                // 🔥 【Final转换修复】使用累积的finalBuffer内容，而不是event.markdown
                val finalContent = if (state.finalBuffer.isNotEmpty()) {
                    state.finalBuffer.toString()
                } else {
                    event.markdown // 兜底使用event中的内容
                }

                Timber.tag("TB-REDUCER").w("📄 [向后兼容] 使用已弃用的FinalArrived事件: ${finalContent.take(50)}...")

                // 🔥 【P0修复】清空缓存的titles
                state.deferredTitles.clear()

                val newState = state.copy(
                    finalMarkdown = finalContent,
                    // 🔥 【冲突修复】移除状态设置，避免与PhaseAnimFinished冲突
                    // isStreaming, isThinkingComplete 等状态由其他事件控制
                    // 只保留final内容的向后兼容处理
                    version = state.version + 1,
                )

                // 🔥 【向后兼容】FinalArrived处理完成
                Timber.tag(
                    "TB-REDUCER",
                ).d("✅ [FinalArrived] 状态更新完成: finalMarkdown长度=${newState.finalMarkdown?.length}")

                newState
            }
        }
    }

    /**
     * 批量处理事件 - 优化性能
     */
    fun reduceBatch(state: ThinkingUiState, events: List<ThinkingEvent>): ThinkingUiState {
        return events.fold(state) { currentState, event ->
            reduce(currentState, event)
        }
    }

    /**
     * 创建初始状态
     * 🔥 【文档规范修复】使用符合baseline文档的数据结构
     */
    fun createInitialState(sessionId: String): ThinkingUiState {
        return ThinkingUiState(
            phases = linkedMapOf(),
            pending = ArrayDeque(),
            sessionId = sessionId,
            startTime = System.currentTimeMillis(),
        )
    }

    /**
     * 重置状态 - 用于新会话
     * 🔥 【文档规范修复】使用符合baseline文档的数据结构
     */
    fun resetState(state: ThinkingUiState, newSessionId: String): ThinkingUiState {
        return ThinkingUiState(
            phases = linkedMapOf(),
            pending = ArrayDeque(),
            sessionId = newSessionId,
            startTime = System.currentTimeMillis(),
        )
    }

    /**
     * 🔥 【MVI 2.0】处理背景Intent
     *
     * @param state 当前状态
     * @param intent 背景Intent
     * @return 更新后的状态
     */
    fun reduceBackgroundIntent(
        state: ThinkingUiState,
        intent: com.example.gymbro.features.thinkingbox.domain.interfaces.ThinkingBoxBackgroundIntent,
    ): ThinkingUiState {
        Timber.tag("TB-BG-REDUCER").d("Processing background intent: ${intent::class.simpleName}")

        return when (intent) {
            is com.example.gymbro.features.thinkingbox.domain.interfaces.ThinkingBoxBackgroundIntent.ToggleBlur -> {
                val newBackgroundState = state.backgroundState.copy(
                    isBlurred = !state.backgroundState.isBlurred,
                )
                state.copy(
                    backgroundState = newBackgroundState,
                    version = state.version + 1,
                )
            }

            is com.example.gymbro.features.thinkingbox.domain.interfaces.ThinkingBoxBackgroundIntent.AdjustBlurRadius -> {
                val validRadius = intent.radius.coerceIn(0f, 50f) // 限制在合理范围内
                val newBackgroundState = state.backgroundState.copy(
                    blurRadius = validRadius,
                    isBlurred = validRadius > 0f, // 自动开启模糊如果半径 > 0
                )
                state.copy(
                    backgroundState = newBackgroundState,
                    version = state.version + 1,
                )
            }

            is com.example.gymbro.features.thinkingbox.domain.interfaces.ThinkingBoxBackgroundIntent.ChangeBackgroundTint -> {
                val newBackgroundState = state.backgroundState.copy(
                    backgroundTint = intent.tint,
                )
                state.copy(
                    backgroundState = newBackgroundState,
                    version = state.version + 1,
                )
            }

            is com.example.gymbro.features.thinkingbox.domain.interfaces.ThinkingBoxBackgroundIntent.ResetBackground -> {
                val newBackgroundState = com.example.gymbro.features.thinkingbox.domain.interfaces.ThinkingBoxBackgroundState()
                state.copy(
                    backgroundState = newBackgroundState,
                    version = state.version + 1,
                )
            }

            is com.example.gymbro.features.thinkingbox.domain.interfaces.ThinkingBoxBackgroundIntent.AutoAdjustBackground -> {
                // 🔥 【智能背景调整】根据主题和内容类型自动选择最佳背景效果
                val (blurRadius, tint) = when (intent.contentType) {
                    com.example.gymbro.features.thinkingbox.domain.interfaces.BackgroundContentType.Default -> {
                        if (intent.isDarkTheme) 8f to 0x40FFFFFF else 6f to 0x20000000
                    }
                    com.example.gymbro.features.thinkingbox.domain.interfaces.BackgroundContentType.Thinking -> {
                        if (intent.isDarkTheme) 12f to 0x60FFFFFF else 10f to 0x30000000
                    }
                    com.example.gymbro.features.thinkingbox.domain.interfaces.BackgroundContentType.Final -> {
                        if (intent.isDarkTheme) 15f to 0x80FFFFFF else 12f to 0x40000000
                    }
                    com.example.gymbro.features.thinkingbox.domain.interfaces.BackgroundContentType.Completed -> {
                        if (intent.isDarkTheme) 5f to 0x30FFFFFF else 4f to 0x15000000
                    }
                }

                val newBackgroundState = state.backgroundState.copy(
                    isBlurred = true,
                    blurRadius = blurRadius,
                    backgroundTint = tint.toInt(),
                )

                Timber.tag("TB-BG-REDUCER").i(
                    "Auto adjusted background: isDark=${intent.isDarkTheme}, " +
                        "type=${intent.contentType}, blur=$blurRadius, tint=$tint",
                )

                state.copy(
                    backgroundState = newBackgroundState,
                    version = state.version + 1,
                )
            }
        }
    }

    /**
     * 状态验证 - 调试用
     */
    fun validateState(state: ThinkingUiState): List<String> {
        val issues = mutableListOf<String>()

        // 检查activePhaseId是否存在于phases中
        if (state.activePhaseId != null && state.activePhaseId !in state.phases) {
            issues.add("Active phase ID not found in phases: ${state.activePhaseId}")
        }

        // 检查pending队列中的phase是否都存在
        state.pending.forEach { phaseId ->
            if (phaseId !in state.phases) {
                issues.add("Pending phase ID not found in phases: $phaseId")
            }
        }

        // 检查是否有重复的phase ID
        if (state.activePhaseId != null && state.activePhaseId in state.pending) {
            issues.add("Active phase ID also in pending queue: ${state.activePhaseId}")
        }

        return issues
    }
}
