package com.example.gymbro.features.thinkingbox.domain.parser

import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.model.TagContext
import com.example.gymbro.features.thinkingbox.domain.model.ThinkingContent
import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.constants.ThinkingBoxStrings
import org.junit.Assert.*
import org.junit.Test

/**
 * ThinkingBox 验证测试
 *
 * 🎯 验证目标：
 * - 确认修复后的架构正确性
 * - 验证 6 个统一事件系统
 * - 测试 <think> → perthink 映射
 * - 验证打字机效果相关组件
 */
class ThinkingBoxValidationTest {

    @Test
    fun `should have consistent 6 unified events between SemanticEvent and ThinkingEvent`() {
        // Given: 6个统一事件的名称
        val expectedEvents = setOf(
            "PreThinkChunk",
            "PreThinkEnd",
            "PhaseStart",
            "PhaseContent",
            "PhaseEnd",
            "FinalArrived",
        )

        // When: 检查 SemanticEvent 中的事件
        val semanticEventClasses = SemanticEvent::class.sealedSubclasses.map { it.simpleName }
        val thinkingEventClasses = ThinkingEvent::class.sealedSubclasses.map { it.simpleName }

        // Then: 验证两个事件系统都包含6个统一事件
        expectedEvents.forEach { eventName ->
            assertTrue(
                "SemanticEvent 应该包含 $eventName",
                semanticEventClasses.contains(eventName),
            )
            assertTrue(
                "ThinkingEvent 应该包含 $eventName",
                thinkingEventClasses.contains(eventName),
            )
        }
    }

    @Test
    fun `should correctly map think tag to perthink phase`() {
        // Given: DomainMapper 和 think 标签相关的 SemanticEvent
        val domainMapper = DomainMapper()
        val phaseStartEvent = SemanticEvent.PhaseStart("perthink", ThinkingBoxStrings.PERTHINK_TITLE)

        // When: 映射事件
        val result = domainMapper.mapSemanticToThinking(phaseStartEvent)

        // Then: 验证映射结果
        assertEquals("应该生成一个事件", 1, result.events.size)
        val thinkingEvent = result.events.first()
        assertTrue("应该是 PhaseStart 事件", thinkingEvent is ThinkingEvent.PhaseStart)

        val phaseStart = thinkingEvent as ThinkingEvent.PhaseStart
        assertEquals("阶段ID应该是 perthink", "perthink", phaseStart.id)
        assertEquals("标题应该是 Bro is thinking", ThinkingBoxStrings.PERTHINK_TITLE, phaseStart.title)
    }

    @Test
    fun `should handle TagContext enhanced functionality`() {
        // Given: 增强的 TagContext
        val thinkContext = TagContext("think", emptyMap())
        val phaseContext = TagContext("phase", mapOf("id" to "1", "title" to "分析阶段"))
        val finalContext = TagContext("final", emptyMap())

        // When & Then: 验证增强功能
        assertTrue("应该识别为预思考标签", thinkContext.isPreThinkTag())
        assertEquals("应该返回 perthink ID", "perthink", thinkContext.getPhaseId())
        assertEquals("应该返回 Bro is thinking 标题", ThinkingBoxStrings.PERTHINK_TITLE, thinkContext.getPhaseTitle())

        assertTrue("应该识别为阶段标签", phaseContext.isPhaseTag())
        assertEquals("应该返回正确的阶段ID", "1", phaseContext.getPhaseId())
        assertEquals("应该返回正确的标题", "分析阶段", phaseContext.attributes["title"])

        assertTrue("应该识别为最终答案标签", finalContext.isFinalTag())

        // 验证 ThinkingBox 标签识别
        assertTrue("think 应该是 ThinkingBox 标签", thinkContext.isThinkingBoxTag())
        assertTrue("phase 应该是 ThinkingBox 标签", phaseContext.isThinkingBoxTag())
        assertTrue("final 应该是 ThinkingBox 标签", finalContext.isThinkingBoxTag())
    }

    @Test
    fun `should create clean ThinkingContent without preset conflicts`() {
        // Given: 清理后的 ThinkingContent 创建
        val phaseId = "test-phase"
        val title = "测试阶段"
        val content = "测试内容"

        // When: 创建 Phase 内容
        val phase = ThinkingContent.Phase.createThinkingPhase(phaseId, title, content)

        // Then: 验证无预设内容冲突
        assertEquals("ID应该正确", phaseId, phase.id)
        assertEquals("标题应该正确", title, phase.title)
        assertEquals("内容应该正确", content, phase.content)

        // 验证 AnnotatedString 转换无硬编码前缀
        val annotatedString = phase.toAnnotatedString()
        assertTrue("应该包含标题", annotatedString.text.contains(title))
        assertTrue("应该包含内容", annotatedString.text.contains(content))
        assertFalse("不应该包含硬编码的【】符号", annotatedString.text.contains("【"))
    }

    @Test
    fun `should create perthink phase with correct defaults`() {
        // Given: perthink 阶段创建
        val content = "预思考内容"

        // When: 创建 perthink 阶段
        val perthinkPhase = ThinkingContent.Phase.createPreThinkPhase(content)

        // Then: 验证 perthink 特殊处理
        assertEquals("ID应该是 perthink", "perthink", perthinkPhase.id)
        assertEquals("标题应该是 Bro is thinking", ThinkingBoxStrings.PERTHINK_TITLE, perthinkPhase.title)
        assertEquals("内容应该正确", content, perthinkPhase.content)
    }

    @Test
    fun `should create TagContext helper methods correctly`() {
        // Given: TagContext 辅助方法
        val perthinkContext = TagContext.createPreThinkContext()
        val phaseContext = TagContext.createPhaseContext("1", "分析阶段")

        // When & Then: 验证辅助方法
        assertEquals("预思考上下文标签名应该是 think", "think", perthinkContext.tagName)
        assertEquals("预思考上下文ID应该是 perthink", "perthink", perthinkContext.attributes["id"])

        assertEquals("阶段上下文标签名应该是 phase", "phase", phaseContext.tagName)
        assertEquals("阶段上下文ID应该正确", "1", phaseContext.attributes["id"])
        assertEquals("阶段上下文标题应该正确", "分析阶段", phaseContext.attributes["title"])
        assertEquals("阶段上下文父标签应该是 thinking", "thinking", phaseContext.parentTag)
    }

    @Test
    fun `should validate XmlStreamScanner basic functionality`() {
        // Given: XmlStreamScanner
        val scanner = XmlStreamScanner()

        // When: 解析简单的 XML 标签
        val tokens = scanner.feed("<think>内容</think>")

        // Then: 验证基本解析功能
        assertTrue("应该生成至少一个 token", tokens.isNotEmpty())

        // 验证循环缓冲区功能
        val bufferInfo = scanner.getBufferInfo()
        assertTrue("缓冲区信息应该包含大小信息", bufferInfo.contains("size="))

        // 验证清理功能
        scanner.clear()
        assertEquals("清理后缓冲区应该为空", "", scanner.getBufferContent())
    }

    @Test
    fun `should validate event mapping consistency`() {
        // Given: 完整的事件映射流程
        val domainMapper = DomainMapper()
        val testEvents = listOf(
            SemanticEvent.PreThinkChunk("预思考"),
            SemanticEvent.PhaseStart("1", "分析"),
            SemanticEvent.PhaseContent("1", "内容"),
            SemanticEvent.PhaseEnd("1"),
            SemanticEvent.FinalArrived("最终答案"),
        )

        // When: 映射所有事件
        var context = DomainMapper.MappingContext()
        val allThinkingEvents = mutableListOf<ThinkingEvent>()

        testEvents.forEach { semanticEvent ->
            val result = domainMapper.mapSemanticToThinking(semanticEvent, context)
            allThinkingEvents.addAll(result.events)
            context = result.context
        }

        // Then: 验证映射一致性
        assertTrue(
            "应该包含 PreThinkChunk",
            allThinkingEvents.any { it is ThinkingEvent.PreThinkChunk },
        )
        assertTrue(
            "应该包含 PhaseStart",
            allThinkingEvents.any { it is ThinkingEvent.PhaseStart },
        )
        assertTrue(
            "应该包含 PhaseContent",
            allThinkingEvents.any { it is ThinkingEvent.PhaseContent },
        )
        assertTrue(
            "应该包含 PhaseEnd",
            allThinkingEvents.any { it is ThinkingEvent.PhaseEnd },
        )
        assertTrue(
            "应该包含 FinalArrived",
            allThinkingEvents.any { it is ThinkingEvent.FinalArrived },
        )
    }
}
